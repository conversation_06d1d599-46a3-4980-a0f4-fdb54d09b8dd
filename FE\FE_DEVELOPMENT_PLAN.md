# Kế hoạch phát triển Frontend - WebTruyen

Đây là tài liệu theo dõi tiến độ phát triển cho ứng dụng Frontend của dự án WebTruyen. Chúng ta sẽ sử dụng Next.js (App Router), TypeScript, và Tailwind CSS để xây dựng một ứng dụng hiện đại, hiệu năng cao và ưu tiên cho thiết bị di động.

## Giai đoạn 1: Thiết lập nền tảng

- [ ] **Khởi tạo dự án Next.js:**
  - [ ] Sử dụng `create-next-app` để tạo một dự án mới trong thư mục `FE`.
  - [ ] Chọn các tùy chọn: TypeScript, ESLint, Tailwind CSS, App Router.
- [ ] **Cấu trúc thư mục:**
  - [ ] Thiết lập cấu trúc thư mục chuẩn cho ứng dụng (components, services, hooks, utils, etc.).
- [ ] **<PERSON><PERSON><PERSON> hình cơ bản:**
  - [ ] C<PERSON><PERSON> hình `tailwind.config.ts` với theme (màu sắc, font chữ) cho dự án.
  - [ ] Thiết lập `layout.tsx` và `page.tsx` gốc.
  - [ ] Tạo các component `Navbar` và `Footer` chung.

## Giai đoạn 2: Xây dựng các tính năng cốt lõi

### 2.1. Giao diện Cào truyện (Scraping)

- [ ] **Trang `/scrape`:**
  - [ ] **Component `ScrapeForm`:**
    - [ ] Tạo một form cho phép người dùng dán URL của truyện cần cào.
    - [ ] Xử lý validation cho input (đảm bảo là URL hợp lệ).
  - [ ] **Tích hợp API:**
    - [ ] Gọi API `POST /scrape/story` khi người dùng submit form.
    - [ ] Sử dụng SWR hoặc React Query để quản lý trạng thái request.
  - [ ] **Hiển thị trạng thái:**
    - [ ] Hiển thị thông báo loading trong khi đang cào.
    - [ ] Hiển thị thông báo thành công hoặc lỗi sau khi API trả về kết quả.

### 2.2. Quản lý và hiển thị truyện

- [ ] **Trang chủ `/` (Dashboard):**
  - [ ] **Tích hợp API:**
    - [ ] Gọi API `GET /api/stories` để lấy danh sách các truyện đã cào.
  - [ ] **Component `StoryList`:**
    - [ ] Hiển thị danh sách truyện dưới dạng lưới (grid).
  - [ ] **Component `StoryCard`:**
    - [ ] Hiển thị thông tin cơ bản của mỗi truyện (ảnh bìa, tên truyện, tác giả).
    - [ ] Khi click vào card, điều hướng đến trang chi tiết truyện.

- [ ] **Trang chi tiết truyện `/stories/[storyId]`:**
  - [ ] **Tích hợp API:**
    - [ ] Gọi API `GET /api/stories/{storyId}` để lấy thông tin chi tiết truyện và danh sách chương.
  - [ ] **Component `StoryInfo`:**
    - [ ] Hiển thị đầy đủ thông tin truyện (ảnh bìa, tên, tác giả, mô tả, trạng thái...).
  - [ ] **Component `ChapterList`:**
    - [ ] Hiển thị danh sách các chương của truyện.
    - [ ] Hỗ trợ phân trang nếu danh sách chương quá dài.
    - [ ] Mỗi chương là một link điều hướng đến trang đọc truyện.

### 2.3. Giao diện đọc truyện

- [ ] **Trang đọc truyện `/stories/[storyId]/[chapterId]`:**
  - [ ] **Tích hợp API:**
    - [ ] Gọi API `GET /api/stories/{storyId}/chapters/{chapterId}` để lấy nội dung chương.
  - [ ] **Component `ReaderView`:**
    - [ ] Hiển thị nội dung chương một cách rõ ràng, dễ đọc.
    - [ ] Tối ưu hóa cho việc đọc trên mobile (font chữ, kích thước, khoảng cách dòng).
  - [ ] **Component `ChapterNavigation`:**
    - [ ] Tạo các nút "Chương trước" và "Chương sau" để chuyển chương.
    - [ ] Thêm một dropdown/select để chọn chương nhanh.
    - [ ] Vô hiệu hóa nút nếu không có chương trước/sau.

## Giai đoạn 3: Hoàn thiện và Tối ưu

- [ ] **Responsive Design:**
  - [ ] Rà soát và đảm bảo tất cả các trang và component hiển thị tốt trên mọi kích thước màn hình (mobile, tablet, desktop).
- [ ] **Xử lý trạng thái (State Handling):**
  - [ ] Implement UI cho các trạng thái loading (với skeleton UI) và error (với thông báo lỗi thân thiện) trên toàn bộ ứng dụng.
- [ ] **Tối ưu hiệu năng (Performance):**
  - [ ] Sử dụng `next/image` cho tất cả hình ảnh để tối ưu hóa.
  - [ ] Tận dụng Server Components và Client Components của Next.js một cách hợp lý.
  - [ ] Kiểm tra và tối ưu tốc độ tải trang.
- [ ] **SEO (Tùy chọn):**
  - [ ] Thêm metadata động cho các trang chi tiết truyện để cải thiện SEO.