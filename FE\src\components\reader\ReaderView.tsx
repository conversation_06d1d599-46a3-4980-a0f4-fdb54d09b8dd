'use client';

import { useState } from 'react';
import { Chapter } from '@/types/story';

interface ReaderViewProps {
  chapter: Chapter;
}

const ReaderView = ({ chapter }: ReaderViewProps) => {
  const [fontSize, setFontSize] = useState(16);
  const [lineHeight, setLineHeight] = useState(1.8);
  const [isDarkMode, setIsDarkMode] = useState(true);

  const increaseFontSize = () => {
    if (fontSize < 24) setFontSize(fontSize + 2);
  };

  const decreaseFontSize = () => {
    if (fontSize > 12) setFontSize(fontSize - 2);
  };

  const increaseLineHeight = () => {
    if (lineHeight < 2.5) setLineHeight(lineHeight + 0.2);
  };

  const decreaseLineHeight = () => {
    if (lineHeight > 1.2) setLineHeight(lineHeight - 0.2);
  };

  return (
    <div className="my-8">
      {/* Reading Controls */}
      <div className="bg-zinc-800/50 rounded-lg p-4 mb-6 border border-zinc-700/50">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Cỡ chữ:</span>
            <button
              onClick={decreaseFontSize}
              className="w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors"
              disabled={fontSize <= 12}
            >
              A-
            </button>
            <span className="text-sm text-white w-8 text-center">{fontSize}</span>
            <button
              onClick={increaseFontSize}
              className="w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors"
              disabled={fontSize >= 24}
            >
              A+
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Giãn dòng:</span>
            <button
              onClick={decreaseLineHeight}
              className="w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors"
              disabled={lineHeight <= 1.2}
            >
              -
            </button>
            <span className="text-sm text-white w-12 text-center">{lineHeight.toFixed(1)}</span>
            <button
              onClick={increaseLineHeight}
              className="w-8 h-8 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors"
              disabled={lineHeight >= 2.5}
            >
              +
            </button>
          </div>

          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            className="flex items-center space-x-2 px-3 py-2 bg-zinc-700 hover:bg-zinc-600 rounded text-white transition-colors"
          >
            {isDarkMode ? (
              <>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">Sáng</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                </svg>
                <span className="text-sm">Tối</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Chapter Content */}
      <div 
        className={`rounded-lg p-6 border transition-colors ${
          isDarkMode 
            ? 'bg-zinc-800/50 border-zinc-700/50 text-gray-100' 
            : 'bg-white border-gray-200 text-gray-900'
        }`}
      >
        <div 
          className="prose prose-lg max-w-none"
          style={{
            fontSize: `${fontSize}px`,
            lineHeight: lineHeight,
            color: isDarkMode ? '#f3f4f6' : '#111827'
          }}
        >
          <div 
            dangerouslySetInnerHTML={{ 
              __html: chapter.enhanced_content || chapter.original_content || 'Nội dung chưa có sẵn'
            }}
          />
        </div>
      </div>

      {/* Chapter Stats */}
      <div className="mt-6 text-center text-sm text-gray-400">
        <p>Chương {chapter.chapter_number} • {chapter.word_count?.toLocaleString() || 0} từ</p>
        <p>Cập nhật: {new Date(chapter.updated_at).toLocaleDateString('vi-VN')}</p>
        <div className="flex justify-center gap-2 mt-2">
          {chapter.is_scraped && (
            <span className="text-xs text-green-400 bg-green-900/30 px-2 py-1 rounded">
              Đã cào
            </span>
          )}
          {chapter.is_enhanced && (
            <span className="text-xs text-blue-400 bg-blue-900/30 px-2 py-1 rounded">
              Đã nâng cấp
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReaderView;