"""
Scraping related API Models
"""
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl, validator

from ..database import StoryStatus
from .common import APIResponse

# ============================================================================
# Story Scraping API Models
# ============================================================================

class StoryInfoRequest(BaseModel):
    """Request to scrape story information"""
    story_url: HttpUrl
    max_pages: int = Field(default=10, ge=1, le=100, description="Maximum number of pages to scrape for chapters")

    @validator('story_url')
    def validate_story_url(cls, v):
        url_str = str(v)
        if 'webtruyen.diendantruyen.com' not in url_str:
            raise ValueError('Only webtruyen.diendantruyen.com URLs are supported')
        return v


class ChapterInfo(BaseModel):
    """Chapter information from scraping"""
    chapter_number: int
    title: str
    url: HttpUrl
    page_number: int

class PageInfo(BaseModel):
    """Page information from scraping"""
    page_number: int
    url: HttpUrl
    chapters: List[ChapterInfo] = Field(default_factory=list)


class StoryInfoResponse(APIResponse):
    """Response from story information scraping"""
    story_id: Optional[str] = None
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    cover_image_url: Optional[HttpUrl] = None
    status: StoryStatus
    total_chapters: int
    total_pages: int
    pages: List[PageInfo] = Field(default_factory=list)
    scraping_job_id: Optional[str] = None


class BatchChapterScrapeRequest(BaseModel):
    """Request to scrape multiple chapters"""
    chapter_urls: List[HttpUrl]
    story_id: Optional[str] = None
    max_concurrent: int = Field(default=3, ge=1, le=10)
    rate_limit_delay: float = Field(default=2.0, ge=0.5, le=10.0)
    
    @validator('chapter_urls')
    def validate_chapter_urls(cls, v):
        if len(v) == 0:
            raise ValueError('At least one chapter URL is required')
        if len(v) > 100:
            raise ValueError('Maximum 100 chapters per batch')
        return v


class ChapterContent(BaseModel):
    """Chapter content from scraping"""
    chapter_number: int
    title: str
    url: HttpUrl
    content: str
    word_count: int
    is_locked: bool = False
    scraping_duration: Optional[float] = None


class BatchChapterScrapeResponse(APIResponse):
    """Response from batch chapter scraping"""
    job_id: str
    total_chapters: int
    chapters: List[ChapterContent] = Field(default_factory=list)
    failed_urls: List[str] = Field(default_factory=list)