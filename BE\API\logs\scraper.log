2025-07-10 15:08:07 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x0000015AE48A9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x0000015AE47B54E0>
           └ <SpawnProcess name='SpawnProcess-72' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000015AE47B4A40>
    └ <SpawnProcess name='SpawnProcess-72' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000015AE736B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-72' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-72' parent=10716 started>
    │    └ <function subprocess_started at 0x0000015AE72EF4C0>
    └ <SpawnProcess name='SpawnProcess-72' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000015AE736BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000015AE72EE660>
           │       │   └ <uvicorn.server.Server object at 0x0000015AE736BA10>
           │       └ <function run at 0x0000015AE48AB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000015AE74482E0>
           │      └ <function Runner.run at 0x0000015AE6FE7A60>
           └ <asyncio.runners.Runner object at 0x0000015AE743C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000015AE6FE5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000015AE743C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000015AE6FE5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000015AE6FE7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000015AEC187C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000015AFF8B4050>
          └ <playwright._impl._connection.Connection object at 0x0000015AFF8B4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000015AE7042C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000015AFF8B4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000015AE6FE6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000015AE6FE4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:08:07 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:08:07 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 368
               │     └ 3
               └ <function _main at 0x0000027D22CF9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 368
           │    └ <function BaseProcess._bootstrap at 0x0000027D22C054E0>
           └ <SpawnProcess name='SpawnProcess-71' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000027D22C04A40>
    └ <SpawnProcess name='SpawnProcess-71' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000027D2576B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-71' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-71' parent=9152 started>
    │    └ <function subprocess_started at 0x0000027D256EF4C0>
    └ <SpawnProcess name='SpawnProcess-71' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=652, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000027D2576BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=652, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000027D256EE660>
           │       │   └ <uvicorn.server.Server object at 0x0000027D2576BA10>
           │       └ <function run at 0x0000027D22CFB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000027D258482E0>
           │      └ <function Runner.run at 0x0000027D24B37A60>
           └ <asyncio.runners.Runner object at 0x0000027D2583C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000027D24B35620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000027D2583C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000027D24B35580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000027D24B37380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000027D2A4C7C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000027D51BE0050>
          └ <playwright._impl._connection.Connection object at 0x0000027D51BE0EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000027D24B92C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000027D51BE0050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000027D24B36E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000027D24B34D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:08:07 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:08:07 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 308
               │     └ 3
               └ <function _main at 0x000001FC75D29BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 308
           │    └ <function BaseProcess._bootstrap at 0x000001FC75C354E0>
           └ <SpawnProcess name='SpawnProcess-79' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001FC75C34A40>
    └ <SpawnProcess name='SpawnProcess-79' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001FC787DB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-79' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-79' parent=11864 started>
    │    └ <function subprocess_started at 0x000001FC7875F4C0>
    └ <SpawnProcess name='SpawnProcess-79' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=600, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001FC787DBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=600, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001FC7875E660>
           │       │   └ <uvicorn.server.Server object at 0x000001FC787DBA10>
           │       └ <function run at 0x000001FC75D2B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001FC788B82E0>
           │      └ <function Runner.run at 0x000001FC77B67A60>
           └ <asyncio.runners.Runner object at 0x000001FC788AC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001FC77B65620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001FC788AC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001FC77B65580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001FC77B67380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001FC7D503C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001FC24D84EC0>
          └ <playwright._impl._connection.Connection object at 0x000001FC24D84590>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001FC77BC2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001FC24D84EC0>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001FC77B66E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001FC77B64D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:08:07 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:08:07 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x000001FFFD479BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x000001FFFD3854E0>
           └ <SpawnProcess name='SpawnProcess-73' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001FFFD384A40>
    └ <SpawnProcess name='SpawnProcess-73' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001FFFFE3B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-73' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-73' parent=33084 started>
    │    └ <function subprocess_started at 0x000001FFFFDBF4C0>
    └ <SpawnProcess name='SpawnProcess-73' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=636, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001FFFFE3BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=636, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001FFFFDBE660>
           │       │   └ <uvicorn.server.Server object at 0x000001FFFFE3BA10>
           │       └ <function run at 0x000001FFFD47B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001FFFFF182E0>
           │      └ <function Runner.run at 0x000001FFFF2B7A60>
           └ <asyncio.runners.Runner object at 0x000001FFFFF0C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001FFFF2B5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001FFFFF0C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001FFFF2B5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001FFFF2B7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001FF84C93C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001FFAC3C4050>
          └ <playwright._impl._connection.Connection object at 0x000001FFAC3C4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001FFFF312C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001FFAC3C4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001FFFF2B6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001FFFF2B4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:08:07 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:08:07 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 384
               │     └ 3
               └ <function _main at 0x0000022EEFEA9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 384
           │    └ <function BaseProcess._bootstrap at 0x0000022EEFDB54E0>
           └ <SpawnProcess name='SpawnProcess-69' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022EEFDB4A40>
    └ <SpawnProcess name='SpawnProcess-69' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000022EF292B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-69' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-69' parent=20612 started>
    │    └ <function subprocess_started at 0x0000022EF28AF4C0>
    └ <SpawnProcess name='SpawnProcess-69' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=600, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000022EF292BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=600, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000022EF28AE660>
           │       │   └ <uvicorn.server.Server object at 0x0000022EF292BA10>
           │       └ <function run at 0x0000022EEFEAB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022EF2A082E0>
           │      └ <function Runner.run at 0x0000022EF1DA7A60>
           └ <asyncio.runners.Runner object at 0x0000022EF29FC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022EF1DA5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022EF29FC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022EF1DA5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022EF1DA7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000022EF76C7C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000022E9EF84590>
          └ <playwright._impl._connection.Connection object at 0x0000022E9EF84EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000022EF1E02C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000022E9EF84590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000022EF1DA6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000022EF1DA4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:08:07 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:10:53 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 344
               │     └ 3
               └ <function _main at 0x0000019161D89BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 344
           │    └ <function BaseProcess._bootstrap at 0x0000019161C954E0>
           └ <SpawnProcess name='SpawnProcess-73' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000019161C94A40>
    └ <SpawnProcess name='SpawnProcess-73' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001916478B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-73' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-73' parent=10716 started>
    │    └ <function subprocess_started at 0x000001916470F4C0>
    └ <SpawnProcess name='SpawnProcess-73' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=656, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001916478BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=656, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001916470E660>
           │       │   └ <uvicorn.server.Server object at 0x000001916478BA10>
           │       └ <function run at 0x0000019161D8B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000191648682E0>
           │      └ <function Runner.run at 0x0000019163BC7A60>
           └ <asyncio.runners.Runner object at 0x000001916485C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000019163BC5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001916485C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000019163BC5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000019163BC7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000191694D7C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000019110D84050>
          └ <playwright._impl._connection.Connection object at 0x0000019110D84EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000019163C22C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000019110D84050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000019163BC6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000019163BC4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:10:53 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:10:53 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 380
               │     └ 3
               └ <function _main at 0x00000295757E9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 380
           │    └ <function BaseProcess._bootstrap at 0x00000295756F54E0>
           └ <SpawnProcess name='SpawnProcess-70' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000295756F4A40>
    └ <SpawnProcess name='SpawnProcess-70' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002957825B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-70' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-70' parent=20612 started>
    │    └ <function subprocess_started at 0x00000295781DF4C0>
    └ <SpawnProcess name='SpawnProcess-70' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002957825BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000295781DE660>
           │       │   └ <uvicorn.server.Server object at 0x000002957825BA10>
           │       └ <function run at 0x00000295757EB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000295783382E0>
           │      └ <function Runner.run at 0x0000029577627A60>
           └ <asyncio.runners.Runner object at 0x000002957832C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029577625620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002957832C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029577625580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029577627380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002957CF47C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000029524774050>
          └ <playwright._impl._connection.Connection object at 0x0000029524774EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000029577682C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000029524774050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000029577626E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000029577624D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:10:53 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:10:53 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x00000264DD379BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x00000264DD2854E0>
           └ <SpawnProcess name='SpawnProcess-72' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000264DD284A40>
    └ <SpawnProcess name='SpawnProcess-72' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000264DFE0B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-72' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-72' parent=9152 started>
    │    └ <function subprocess_started at 0x00000264DFD8F4C0>
    └ <SpawnProcess name='SpawnProcess-72' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000264DFE0BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000264DFD8E660>
           │       │   └ <uvicorn.server.Server object at 0x00000264DFE0BA10>
           │       └ <function run at 0x00000264DD37B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000264DFEE82E0>
           │      └ <function Runner.run at 0x00000264DF287A60>
           └ <asyncio.runners.Runner object at 0x00000264DFEDC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000264DF285620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000264DFEDC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000264DF285580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000264DF287380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000264E4C37C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000264FFF94050>
          └ <playwright._impl._connection.Connection object at 0x00000264FFF94EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x00000264DF2E2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000264FFF94050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000264DF286E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000264DF284D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:10:53 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:10:53 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x00000200252B9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x00000200251C54E0>
           └ <SpawnProcess name='SpawnProcess-74' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000200251C4A40>
    └ <SpawnProcess name='SpawnProcess-74' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020027D7B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-74' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-74' parent=33084 started>
    │    └ <function subprocess_started at 0x0000020027CFF4C0>
    └ <SpawnProcess name='SpawnProcess-74' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020027D7BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020027CFE660>
           │       │   └ <uvicorn.server.Server object at 0x0000020027D7BA10>
           │       └ <function run at 0x00000200252BB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020027E582E0>
           │      └ <function Runner.run at 0x00000200271F7A60>
           └ <asyncio.runners.Runner object at 0x0000020027E4C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000200271F5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020027E4C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000200271F5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000200271F7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002002CB27C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000020054274050>
          └ <playwright._impl._connection.Connection object at 0x0000020054274EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000020027252C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000020054274050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000200271F6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000200271F4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:10:53 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:10:53 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x00000171F5679BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x00000171F55854E0>
           └ <SpawnProcess name='SpawnProcess-80' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000171F5584A40>
    └ <SpawnProcess name='SpawnProcess-80' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000171F812B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-80' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-80' parent=11864 started>
    │    └ <function subprocess_started at 0x00000171F80AF4C0>
    └ <SpawnProcess name='SpawnProcess-80' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=664, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000171F812BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=664, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000171F80AE660>
           │       │   └ <uvicorn.server.Server object at 0x00000171F812BA10>
           │       └ <function run at 0x00000171F567B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000171F82082E0>
           │      └ <function Runner.run at 0x00000171F74B7A60>
           └ <asyncio.runners.Runner object at 0x00000171F81FC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000171F74B5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000171F81FC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000171F74B5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000171F74B7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000171FCE47C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000171A4774050>
          └ <playwright._impl._connection.Connection object at 0x00000171A4774EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x00000171F7512C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000171A4774050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000171F74B6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000171F74B4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:10:53 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:48:23 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 356
               │     └ 3
               └ <function _main at 0x0000022D2CBC9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 356
           │    └ <function BaseProcess._bootstrap at 0x0000022D2CAD54E0>
           └ <SpawnProcess name='SpawnProcess-1' parent=29496 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022D2CAD4A40>
    └ <SpawnProcess name='SpawnProcess-1' parent=29496 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000022D2CAAAF90>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=29496 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=29496 started>
    │    └ <function subprocess_started at 0x0000022D2F6287C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=29496 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=960, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000022D5B44DBE0>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=960, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000022D2F61B880>
           │       │   └ <uvicorn.server.Server object at 0x0000022D5B44DBE0>
           │       └ <function run at 0x0000022D2D1DFEC0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022D5B4F26C0>
           │      └ <function Runner.run at 0x0000022D2EB6C540>
           └ <asyncio.runners.Runner object at 0x0000022D5B44D6A0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022D2EB72020>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022D5B44D6A0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022D2EB71F80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022D2EB73D80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000022D343377E0>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000022D5BB44440>
          └ <playwright._impl._connection.Connection object at 0x0000022D5BB44C20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000022D2EB6F740>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000022D5BB44440>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000022D2EB73880>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000022D2EB71760>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:48:23 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:55:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 360
               │     └ 3
               └ <function _main at 0x000002B452AA9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 360
           │    └ <function BaseProcess._bootstrap at 0x000002B4529B54E0>
           └ <SpawnProcess name='SpawnProcess-1' parent=34276 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002B4529B4A40>
    └ <SpawnProcess name='SpawnProcess-1' parent=34276 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002B45298AF90>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34276 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34276 started>
    │    └ <function subprocess_started at 0x000002B4554D87C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34276 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=808, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002B47F3DDBE0>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=808, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002B4554CB880>
           │       │   └ <uvicorn.server.Server object at 0x000002B47F3DDBE0>
           │       └ <function run at 0x000002B4530BFEC0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B47F4826C0>
           │      └ <function Runner.run at 0x000002B454A1C540>
           └ <asyncio.runners.Runner object at 0x000002B47F3DD6A0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B454A22020>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B47F3DD6A0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002B454A21F80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B454A23D80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002B45A2C77E0>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000002B47FAD4440>
          └ <playwright._impl._connection.Connection object at 0x000002B47FAD4C20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000002B454A1F740>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000002B47FAD4440>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000002B454A23880>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000002B454A21760>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:55:11 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 348
               │     └ 3
               └ <function _main at 0x000002BB65A49BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 348
           │    └ <function BaseProcess._bootstrap at 0x000002BB659554E0>
           └ <SpawnProcess name='SpawnProcess-2' parent=34276 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002BB65954A40>
    └ <SpawnProcess name='SpawnProcess-2' parent=34276 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002BB6592AF90>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-2' parent=34276 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-2' parent=34276 started>
    │    └ <function subprocess_started at 0x000002BB684787C0>
    └ <SpawnProcess name='SpawnProcess-2' parent=34276 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002BB143CDBE0>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002BB6846B880>
           │       │   └ <uvicorn.server.Server object at 0x000002BB143CDBE0>
           │       └ <function run at 0x000002BB6605FEC0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002BB144726C0>
           │      └ <function Runner.run at 0x000002BB679BC540>
           └ <asyncio.runners.Runner object at 0x000002BB143CD6A0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002BB679C2020>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002BB143CD6A0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002BB679C1F80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002BB679C3D80>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002BB6D1BF7E0>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000002BB14BB4440>
          └ <playwright._impl._connection.Connection object at 0x000002BB14BB4C20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000002BB679BF740>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000002BB14BB4440>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000002BB679C3880>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000002BB679C1760>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:11 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 368
               │     └ 3
               └ <function _main at 0x0000028BB3CB9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 368
           │    └ <function BaseProcess._bootstrap at 0x0000028BB3BC54E0>
           └ <SpawnProcess name='SpawnProcess-81' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000028BB3BC4A40>
    └ <SpawnProcess name='SpawnProcess-81' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000028BB67BB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-81' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-81' parent=11864 started>
    │    └ <function subprocess_started at 0x0000028BB673F4C0>
    └ <SpawnProcess name='SpawnProcess-81' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=604, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000028BB67BBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=604, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000028BB673E660>
           │       │   └ <uvicorn.server.Server object at 0x0000028BB67BBA10>
           │       └ <function run at 0x0000028BB3CBB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028BB68982E0>
           │      └ <function Runner.run at 0x0000028BB5B77A60>
           └ <asyncio.runners.Runner object at 0x0000028BB688C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028BB5B75620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028BB688C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000028BB5B75580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028BB5B77380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000028BBB647C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000028BE2D64050>
          └ <playwright._impl._connection.Connection object at 0x0000028BE2D64EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000028BB5BD2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000028BE2D64050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000028BB5B76E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000028BB5B74D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:11 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000002375B3D9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000002375B2E54E0>
           └ <SpawnProcess name='SpawnProcess-71' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002375B2E4A40>
    └ <SpawnProcess name='SpawnProcess-71' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002375DDCB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-71' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-71' parent=20612 started>
    │    └ <function subprocess_started at 0x000002375DD4F4C0>
    └ <SpawnProcess name='SpawnProcess-71' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002375DDCBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002375DD4E660>
           │       │   └ <uvicorn.server.Server object at 0x000002375DDCBA10>
           │       └ <function run at 0x000002375B3DB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002375DEA82E0>
           │      └ <function Runner.run at 0x000002375D217A60>
           └ <asyncio.runners.Runner object at 0x000002375DE9C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002375D215620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002375DE9C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002375D215580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002375D217380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000023762B97C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000002377FF34050>
          └ <playwright._impl._connection.Connection object at 0x000002377FF34EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000002375D272C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000002377FF34050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000002375D216E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000002375D214D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:11 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001C5F3839BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001C5F37454E0>
           └ <SpawnProcess name='SpawnProcess-76' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001C5F3744A40>
    └ <SpawnProcess name='SpawnProcess-76' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001C5F61FB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-76' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-76' parent=33084 started>
    │    └ <function subprocess_started at 0x000001C5F617F4C0>
    └ <SpawnProcess name='SpawnProcess-76' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001C5F61FBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001C5F617E660>
           │       │   └ <uvicorn.server.Server object at 0x000001C5F61FBA10>
           │       └ <function run at 0x000001C5F383B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001C5F62D82E0>
           │      └ <function Runner.run at 0x000001C5F5677A60>
           └ <asyncio.runners.Runner object at 0x000001C5F62CC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001C5F5675620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001C5F62CC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001C5F5675580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001C5F5677380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001C5FAFF3C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001C5A2884050>
          └ <playwright._impl._connection.Connection object at 0x000001C5A2884EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001C5F56D2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001C5A2884050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001C5F5676E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001C5F5674D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:11 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 184
               │     └ 3
               └ <function _main at 0x00000172A5009BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 184
           │    └ <function BaseProcess._bootstrap at 0x00000172A4F154E0>
           └ <SpawnProcess name='SpawnProcess-75' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000172A4F14A40>
    └ <SpawnProcess name='SpawnProcess-75' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000172A7ACB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-75' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-75' parent=10716 started>
    │    └ <function subprocess_started at 0x00000172A7A4F4C0>
    └ <SpawnProcess name='SpawnProcess-75' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=336, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000172A7ACBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=336, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000172A7A4E660>
           │       │   └ <uvicorn.server.Server object at 0x00000172A7ACBA10>
           │       └ <function run at 0x00000172A500B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000172A7BA82E0>
           │      └ <function Runner.run at 0x00000172A6E47A60>
           └ <asyncio.runners.Runner object at 0x00000172A7B9C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000172A6E45620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000172A7B9C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000172A6E45580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000172A6E47380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000172AC837C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000172D3F94590>
          └ <playwright._impl._connection.Connection object at 0x00000172D3F94EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x00000172A6EA2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000172D3F94590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000172A6E46E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000172A6E44D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:12 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:12 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:56:12 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 356
               │     └ 3
               └ <function _main at 0x0000027139FD9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 356
           │    └ <function BaseProcess._bootstrap at 0x0000027139EE54E0>
           └ <SpawnProcess name='SpawnProcess-74' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000027139EE4A40>
    └ <SpawnProcess name='SpawnProcess-74' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002713CA0B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-74' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-74' parent=9152 started>
    │    └ <function subprocess_started at 0x000002713C98F4C0>
    └ <SpawnProcess name='SpawnProcess-74' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=632, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002713CA0BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=632, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002713C98E660>
           │       │   └ <uvicorn.server.Server object at 0x000002713CA0BA10>
           │       └ <function run at 0x0000027139FDB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002713CAE82E0>
           │      └ <function Runner.run at 0x000002713BE17A60>
           └ <asyncio.runners.Runner object at 0x000002713CADC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002713BE15620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002713CADC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002713BE15580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002713BE17380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000027141857C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000027168F74D70>
          └ <playwright._impl._connection.Connection object at 0x0000027168F74EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000002713BE72C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000027168F74D70>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000002713BE16E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000002713BE14D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:56:12 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:57:29 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x0000022C8C839BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x0000022C8C7454E0>
           └ <SpawnProcess name='SpawnProcess-72' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022C8C744A40>
    └ <SpawnProcess name='SpawnProcess-72' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000022C8F2CB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-72' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-72' parent=20612 started>
    │    └ <function subprocess_started at 0x0000022C8F24F4C0>
    └ <SpawnProcess name='SpawnProcess-72' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=416, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000022C8F2CBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=416, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000022C8F24E660>
           │       │   └ <uvicorn.server.Server object at 0x0000022C8F2CBA10>
           │       └ <function run at 0x0000022C8C83B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022C8F3A82E0>
           │      └ <function Runner.run at 0x0000022C8E687A60>
           └ <asyncio.runners.Runner object at 0x0000022C8F39C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022C8E685620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022C8F39C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022C8E685580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022C8E687380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000022C940C3C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000022CBB824590>
          └ <playwright._impl._connection.Connection object at 0x0000022CBB824EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000022C8E6E2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000022CBB824590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000022C8E686E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000022C8E684D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:57:29 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:57:29 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x0000029579469BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000295793754E0>
           └ <SpawnProcess name='SpawnProcess-76' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029579374A40>
    └ <SpawnProcess name='SpawnProcess-76' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002957BE2B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-76' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-76' parent=10716 started>
    │    └ <function subprocess_started at 0x000002957BDAF4C0>
    └ <SpawnProcess name='SpawnProcess-76' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=624, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002957BE2BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=624, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002957BDAE660>
           │       │   └ <uvicorn.server.Server object at 0x000002957BE2BA10>
           │       └ <function run at 0x000002957946B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002957BF082E0>
           │      └ <function Runner.run at 0x000002957B2A7A60>
           └ <asyncio.runners.Runner object at 0x000002957BEFC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002957B2A5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002957BEFC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002957B2A5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002957B2A7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002957FC17C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000029528458590>
          └ <playwright._impl._connection.Connection object at 0x0000029528458EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000002957B302C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000029528458590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000002957B2A6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000002957B2A4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:57:29 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:57:29 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 308
               │     └ 3
               └ <function _main at 0x000001F166F39BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 308
           │    └ <function BaseProcess._bootstrap at 0x000001F166E454E0>
           └ <SpawnProcess name='SpawnProcess-77' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001F166E44A40>
    └ <SpawnProcess name='SpawnProcess-77' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001F1698FB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-77' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-77' parent=33084 started>
    │    └ <function subprocess_started at 0x000001F16987F4C0>
    └ <SpawnProcess name='SpawnProcess-77' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=632, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001F1698FBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=632, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001F16987E660>
           │       │   └ <uvicorn.server.Server object at 0x000001F1698FBA10>
           │       └ <function run at 0x000001F166F3B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001F1699D82E0>
           │      └ <function Runner.run at 0x000001F168D77A60>
           └ <asyncio.runners.Runner object at 0x000001F1699CC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001F168D75620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001F1699CC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001F168D75580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F168D77380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001F16E747C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001F17FE64050>
          └ <playwright._impl._connection.Connection object at 0x000001F17FE64EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001F168DD2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001F17FE64050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001F168D76E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001F168D74D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:57:29 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x0000027C27139BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x0000027C270454E0>
           └ <SpawnProcess name='SpawnProcess-82' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000027C27044A40>
    └ <SpawnProcess name='SpawnProcess-82' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000027C29B8B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-82' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-82' parent=11864 started>
    │    └ <function subprocess_started at 0x0000027C29B0F4C0>
    └ <SpawnProcess name='SpawnProcess-82' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000027C29B8BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000027C29B0E660>
           │       │   └ <uvicorn.server.Server object at 0x0000027C29B8BA10>
           │       └ <function run at 0x0000027C2713B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000027C29C682E0>
           │      └ <function Runner.run at 0x0000027C29007A60>
           └ <asyncio.runners.Runner object at 0x0000027C29C5C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000027C29005620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000027C29C5C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000027C29005580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000027C29007380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000027C2E9A7C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000027C560F4590>
          └ <playwright._impl._connection.Connection object at 0x0000027C560F4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000027C29062C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000027C560F4590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000027C29006E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000027C29004D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:57:29 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:57:29 | ERROR | ❌ Failed to initialize services: 
2025-07-10 15:57:29 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 200
               │     └ 3
               └ <function _main at 0x0000029884EB9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 200
           │    └ <function BaseProcess._bootstrap at 0x0000029884DC54E0>
           └ <SpawnProcess name='SpawnProcess-75' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029884DC4A40>
    └ <SpawnProcess name='SpawnProcess-75' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000298878BB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-75' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-75' parent=9152 started>
    │    └ <function subprocess_started at 0x000002988783F4C0>
    └ <SpawnProcess name='SpawnProcess-75' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000298878BBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=612, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002988783E660>
           │       │   └ <uvicorn.server.Server object at 0x00000298878BBA10>
           │       └ <function run at 0x0000029884EBB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000298879982E0>
           │      └ <function Runner.run at 0x0000029886CF7A60>
           └ <asyncio.runners.Runner object at 0x000002988798C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029886CF5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002988798C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029886CF5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029886CF7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002988C797C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000298B3EB4050>
          └ <playwright._impl._connection.Connection object at 0x00000298B3EB4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000029886D52C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000298B3EB4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000029886CF6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000029886CF4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 15:57:29 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:25 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x000001ED15629BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x000001ED155354E0>
           └ <SpawnProcess name='SpawnProcess-83' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001ED15534A40>
    └ <SpawnProcess name='SpawnProcess-83' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001ED17FEB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-83' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-83' parent=11864 started>
    │    └ <function subprocess_started at 0x000001ED17F6F4C0>
    └ <SpawnProcess name='SpawnProcess-83' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001ED17FEBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001ED17F6E660>
           │       │   └ <uvicorn.server.Server object at 0x000001ED17FEBA10>
           │       └ <function run at 0x000001ED1562B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001ED180C82E0>
           │      └ <function Runner.run at 0x000001ED17467A60>
           └ <asyncio.runners.Runner object at 0x000001ED180BC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001ED17465620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001ED180BC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001ED17465580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001ED17467380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001ED1CF17C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001ED44634050>
          └ <playwright._impl._connection.Connection object at 0x000001ED44634EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001ED174C2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001ED44634050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001ED17466E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001ED17464D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:25 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:25 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 396
               │     └ 3
               └ <function _main at 0x0000022428109BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 396
           │    └ <function BaseProcess._bootstrap at 0x00000224280154E0>
           └ <SpawnProcess name='SpawnProcess-78' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022428014A40>
    └ <SpawnProcess name='SpawnProcess-78' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002242AB3B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-78' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-78' parent=33084 started>
    │    └ <function subprocess_started at 0x000002242AABF4C0>
    └ <SpawnProcess name='SpawnProcess-78' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=624, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002242AB3BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=624, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002242AABE660>
           │       │   └ <uvicorn.server.Server object at 0x000002242AB3BA10>
           │       └ <function run at 0x000002242810B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002242AC182E0>
           │      └ <function Runner.run at 0x0000022429F47A60>
           └ <asyncio.runners.Runner object at 0x000002242AC0C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022429F45620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002242AC0C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022429F45580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022429F47380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002242F947C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000022457064050>
          └ <playwright._impl._connection.Connection object at 0x0000022457064EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000022429FA2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000022457064050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000022429F46E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000022429F44D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:25 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:25 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001DDDA679BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001DDDA5854E0>
           └ <SpawnProcess name='SpawnProcess-76' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001DDDA584A40>
    └ <SpawnProcess name='SpawnProcess-76' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001DDDD05B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-76' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-76' parent=9152 started>
    │    └ <function subprocess_started at 0x000001DDDCFDF4C0>
    └ <SpawnProcess name='SpawnProcess-76' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=644, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001DDDD05BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=644, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001DDDCFDE660>
           │       │   └ <uvicorn.server.Server object at 0x000001DDDD05BA10>
           │       └ <function run at 0x000001DDDA67B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001DDDD1382E0>
           │      └ <function Runner.run at 0x000001DDDCCB7A60>
           └ <asyncio.runners.Runner object at 0x000001DDDD12C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001DDDCCB5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001DDDD12C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001DDDCCB5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001DDDCCB7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001DDE1E57C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001DDFF5A0050>
          └ <playwright._impl._connection.Connection object at 0x000001DDFF5A0EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001DDDCD12C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001DDFF5A0050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001DDDCCB6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001DDDCCB4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:25 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:25 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 364
               │     └ 3
               └ <function _main at 0x0000020CEE3C9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 364
           │    └ <function BaseProcess._bootstrap at 0x0000020CEE2D54E0>
           └ <SpawnProcess name='SpawnProcess-73' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000020CEE2D4A40>
    └ <SpawnProcess name='SpawnProcess-73' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020CF0E4B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-73' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-73' parent=20612 started>
    │    └ <function subprocess_started at 0x0000020CF0DCF4C0>
    └ <SpawnProcess name='SpawnProcess-73' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020CF0E4BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020CF0DCE660>
           │       │   └ <uvicorn.server.Server object at 0x0000020CF0E4BA10>
           │       └ <function run at 0x0000020CEE3CB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020CF0F282E0>
           │      └ <function Runner.run at 0x0000020CF02C7A60>
           └ <asyncio.runners.Runner object at 0x0000020CF0F1C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020CF02C5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020CF0F1C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020CF02C5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020CF02C7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000020CF5C77C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000020C9D584050>
          └ <playwright._impl._connection.Connection object at 0x0000020C9D584EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000020CF0322C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000020C9D584050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000020CF02C6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000020CF02C4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:25 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 344
               │     └ 3
               └ <function _main at 0x0000029804C39BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 344
           │    └ <function BaseProcess._bootstrap at 0x0000029804B454E0>
           └ <SpawnProcess name='SpawnProcess-77' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029804B44A40>
    └ <SpawnProcess name='SpawnProcess-77' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000298076DB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-77' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-77' parent=10716 started>
    │    └ <function subprocess_started at 0x000002980765F4C0>
    └ <SpawnProcess name='SpawnProcess-77' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000298076DBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=628, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002980765E660>
           │       │   └ <uvicorn.server.Server object at 0x00000298076DBA10>
           │       └ <function run at 0x0000029804C3B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000298077B82E0>
           │      └ <function Runner.run at 0x0000029806A77A60>
           └ <asyncio.runners.Runner object at 0x00000298077AC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029806A75620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000298077AC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029806A75580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029806A77380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000002980C477C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000029833BC4050>
          └ <playwright._impl._connection.Connection object at 0x0000029833BC4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000029806AD2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000029833BC4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000029806A76E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000029806A74D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:25 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:25 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:28 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 192
               │     └ 3
               └ <function _main at 0x0000018758C79BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 192
           │    └ <function BaseProcess._bootstrap at 0x0000018758B854E0>
           └ <SpawnProcess name='SpawnProcess-79' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000018758B84A40>
    └ <SpawnProcess name='SpawnProcess-79' parent=33084 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001875B6EB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-79' parent=33084 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-79' parent=33084 started>
    │    └ <function subprocess_started at 0x000001875B66F4C0>
    └ <SpawnProcess name='SpawnProcess-79' parent=33084 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=592, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001875B6EBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=592, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001875B66E660>
           │       │   └ <uvicorn.server.Server object at 0x000001875B6EBA10>
           │       └ <function run at 0x0000018758C7B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001875B7C82E0>
           │      └ <function Runner.run at 0x000001875AAF7A60>
           └ <asyncio.runners.Runner object at 0x000001875B7BC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001875AAF5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001875B7BC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001875AAF5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001875AAF7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000018760593C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001877FCB4590>
          └ <playwright._impl._connection.Connection object at 0x000001877FCB4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001875AB52C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001877FCB4590>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001875AAF6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001875AAF4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:28 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:28 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 372
               │     └ 3
               └ <function _main at 0x000001E2B9529BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 372
           │    └ <function BaseProcess._bootstrap at 0x000001E2B94354E0>
           └ <SpawnProcess name='SpawnProcess-84' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E2B9434A40>
    └ <SpawnProcess name='SpawnProcess-84' parent=11864 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E2BBF2B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-84' parent=11864 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-84' parent=11864 started>
    │    └ <function subprocess_started at 0x000001E2BBEAF4C0>
    └ <SpawnProcess name='SpawnProcess-84' parent=11864 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=584, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E2BBF2BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=584, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E2BBEAE660>
           │       │   └ <uvicorn.server.Server object at 0x000001E2BBF2BA10>
           │       └ <function run at 0x000001E2B952B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E2BC0082E0>
           │      └ <function Runner.run at 0x000001E2BB367A60>
           └ <asyncio.runners.Runner object at 0x000001E2BBFFC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E2BB365620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E2BBFFC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E2BB365580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E2BB367380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x000001E2C0D87C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x000001E2E84C4050>
          └ <playwright._impl._connection.Connection object at 0x000001E2E84C4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x000001E2BB3C2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x000001E2E84C4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x000001E2BB366E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x000001E2BB364D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:28 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:28 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 352
               │     └ 3
               └ <function _main at 0x00000284E55B9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 352
           │    └ <function BaseProcess._bootstrap at 0x00000284E54C54E0>
           └ <SpawnProcess name='SpawnProcess-74' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000284E54C4A40>
    └ <SpawnProcess name='SpawnProcess-74' parent=20612 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000284E7F9B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-74' parent=20612 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-74' parent=20612 started>
    │    └ <function subprocess_started at 0x00000284E7F1F4C0>
    └ <SpawnProcess name='SpawnProcess-74' parent=20612 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=596, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000284E7F9BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=596, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000284E7F1E660>
           │       │   └ <uvicorn.server.Server object at 0x00000284E7F9BA10>
           │       └ <function run at 0x00000284E55BB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000284E80782E0>
           │      └ <function Runner.run at 0x00000284E73F7A60>
           └ <asyncio.runners.Runner object at 0x00000284E806C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000284E73F5620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000284E806C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000284E73F5580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000284E73F7380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000284ECE67C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x0000028494584050>
          └ <playwright._impl._connection.Connection object at 0x0000028494584EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x00000284E7452C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x0000028494584050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000284E73F6E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000284E73F4D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:28 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:28 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 352
               │     └ 3
               └ <function _main at 0x0000029781BE9BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 352
           │    └ <function BaseProcess._bootstrap at 0x0000029781AF54E0>
           └ <SpawnProcess name='SpawnProcess-77' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029781AF4A40>
    └ <SpawnProcess name='SpawnProcess-77' parent=9152 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000297846CB8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-77' parent=9152 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-77' parent=9152 started>
    │    └ <function subprocess_started at 0x000002978464F4C0>
    └ <SpawnProcess name='SpawnProcess-77' parent=9152 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=608, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000297846CBA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=608, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002978464E660>
           │       │   └ <uvicorn.server.Server object at 0x00000297846CBA10>
           │       └ <function run at 0x0000029781BEB2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000297847A82E0>
           │      └ <function Runner.run at 0x0000029783B47A60>
           └ <asyncio.runners.Runner object at 0x000002978479C830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029783B45620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002978479C830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029783B45580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029783B47380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x0000029789487C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000297B0BE4050>
          └ <playwright._impl._connection.Connection object at 0x00000297B0BE4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x0000029783BA2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000297B0BE4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x0000029783B46E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x0000029783B44D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:28 | ERROR | ❌ Failed to initialize services: 
2025-07-10 16:03:28 | ERROR | Task exception was never retrieved
future: <Task finished name='Task-4' coro=<Connection.run() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 352
               │     └ 3
               └ <function _main at 0x00000170A3459BC0>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 352
           │    └ <function BaseProcess._bootstrap at 0x00000170A33654E0>
           └ <SpawnProcess name='SpawnProcess-78' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000170A3364A40>
    └ <SpawnProcess name='SpawnProcess-78' parent=10716 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000170A5F1B8C0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-78' parent=10716 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-78' parent=10716 started>
    │    └ <function subprocess_started at 0x00000170A5E9F4C0>
    └ <SpawnProcess name='SpawnProcess-78' parent=10716 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000170A5F1BA10>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=616, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000170A5E9E660>
           │       │   └ <uvicorn.server.Server object at 0x00000170A5F1BA10>
           │       └ <function run at 0x00000170A345B2E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000170A5FF82E0>
           │      └ <function Runner.run at 0x00000170A5397A60>
           └ <asyncio.runners.Runner object at 0x00000170A5FEC830>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000170A5395620>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000170A5FEC830>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000170A5395580>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000170A5397380>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2043, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.
> File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
          │    │          └ <function PipeTransport.connect at 0x00000170AAC87C40>
          │    └ <playwright._impl._transport.PipeTransport object at 0x00000170D23C4050>
          └ <playwright._impl._connection.Connection object at 0x00000170D23C4EC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
    │                  │       └ <function create_subprocess_exec at 0x00000170A53F2C00>
    │                  └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
    └ <playwright._impl._transport.PipeTransport object at 0x00000170D23C4050>
  File "C:\Python313\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                                │    └ <function BaseEventLoop.subprocess_exec at 0x00000170A5396E80>
                                └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                      │    └ <function BaseEventLoop._make_subprocess_transport at 0x00000170A5394D60>
                      └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError

NotImplementedError
2025-07-10 16:03:28 | ERROR | ❌ Failed to initialize services: 
2025-07-14 13:26:24 | INFO | MetruyenScraper started
2025-07-14 13:26:24 | INFO | ✅ Scraper started successfully
2025-07-14 13:26:24 | INFO | 🚀 Scraping story info from: https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/
2025-07-14 13:26:24 | INFO | 📄 Navigating to page...
2025-07-14 13:26:31 | INFO | ✅ Page loaded successfully
2025-07-14 13:26:31 | INFO | 💾 HTML saved to: D:\Personal Projects\Vibe\Webtruyen\BE\Scraper\html_debug\_truyen_phan_phai_tieng_long_bi_nu_chinh_nghe_len_ve_sau_noi_dung_cot_truyen_sap_.html
2025-07-14 13:26:31 | INFO | 🔍 Extracting story info using strategy...
2025-07-14 13:26:31 | INFO | 🔍 WebtruyenStrategy config keys: ['base_url', 'selectors', 'wait_conditions']
2025-07-14 13:26:31 | INFO | 🔍 Selectors loaded: 16 items - ['content', 'title', 'next_chapter', 'prev_chapter', 'chapter_list', 'chapter_link', 'story_title', 'story_image', 'story_author', 'story_description', 'meta_description', 'meta_keywords', 'story_chapters', 'story_pagination', 'story_pagination_link', 'locked_content']
2025-07-14 13:26:31 | INFO | 🔍 Extracting title with selectors: {'content': '.chapter-content, .story-content, .content-area p', 'title': 'h1, .entry-title, .chapter-title, h3', 'next_chapter': 'a[id="next-link"], a[href*="chuong-"]:contains("Sau"), .nav-next a', 'prev_chapter': 'a[id="prev-link"], a[href*="chuong-"]:contains("Trước"), .nav-previous a', 'chapter_list': '#chapter-list-tab, .chapter-list, .story-chapters', 'chapter_link': 'a[class*="uk-link-toggle"]', 'story_title': '#category-title', 'story_image': 'img[src^="https://i0.wp"]', 'story_author': 'div:contains("Tác giả") + a[href*="tac-gia"]', 'story_description': 'div.hide-long-text.uk-position-relative.uk-text-justify', 'meta_description': 'meta[name="description"]', 'meta_keywords': 'meta[name="keywords"]', 'story_chapters': '#chapter-list-tab, .chapter-list, .story-chapters', 'story_pagination': 'ul.uk-pagination.uk-flex-center', 'story_pagination_link': 'ul.uk-pagination.uk-flex-center a', 'locked_content': '.premium-content, .vip-content, .locked-content, .paywall'}
2025-07-14 13:26:31 | INFO | 📝 Trying story_title selector: #category-title
2025-07-14 13:26:31 | INFO | ✅ Found title: 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập'
2025-07-14 13:26:31 | INFO | ✅ Found description: 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?

X...'
2025-07-14 13:26:31 | INFO | ✅ Found cover image: 'https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'
2025-07-14 13:26:31 | WARNING | ❌ No author found with selector: div:contains("Tác giả") + a[href*="tac-gia"]
2025-07-14 13:26:31 | INFO | ℹ️ No story_total_chapters selector configured
2025-07-14 13:26:31 | INFO | 📖 Story info extracted: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:26:31 | INFO | 📊 Story info extracted: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:26:31 | INFO | 📖 Title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:26:31 | INFO | 👤 Author: Unknown
2025-07-14 13:26:31 | INFO | 📝 Description: Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?

X...
2025-07-14 13:26:31 | WARNING | Missing required field 'timestamp' in data
2025-07-14 13:26:31 | WARNING | Missing required field 'timestamp' in data
2025-07-14 13:26:31 | ERROR | Failed to extract total chapters: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FF862650>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245DD421A30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8B240>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245DD421A30>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 43, in scrape_story_hierarchy
    story_data = await self._scrape_story_info(story_url, request)
                       │    │                  │          └ <starlette.requests.Request object at 0x00000245DD421A30>
                       │    │                  └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                       │    └ <function HierarchicalScrapingService._scrape_story_info at 0x00000245E008C900>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 73, in _scrape_story_info
    total_chapters = await self._extract_total_chapters(story_info, request, story_url)
                           │    │                       │           │        └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                           │    │                       │           └ <starlette.requests.Request object at 0x00000245DD421A30>
                           │    │                       └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                           │    └ <function HierarchicalScrapingService._extract_total_chapters at 0x00000245E008C9A0>
                           └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 100, in _extract_total_chapters
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:26:31 | ERROR | Failed to generate page URLs: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FF862650>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245DD421A30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8B240>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245DD421A30>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 43, in scrape_story_hierarchy
    story_data = await self._scrape_story_info(story_url, request)
                       │    │                  │          └ <starlette.requests.Request object at 0x00000245DD421A30>
                       │    │                  └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                       │    └ <function HierarchicalScrapingService._scrape_story_info at 0x00000245E008C900>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 76, in _scrape_story_info
    page_urls = await self._generate_page_urls(story_url, request)
                      │    │                   │          └ <starlette.requests.Request object at 0x00000245DD421A30>
                      │    │                   └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                      │    └ <function HierarchicalScrapingService._generate_page_urls at 0x00000245E008CA40>
                      └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 133, in _generate_page_urls
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:26:31 | INFO | 📄 Phase 2: Processing 1 pages
2025-07-14 13:26:31 | INFO | 📄 Processing page 1/1: https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/
2025-07-14 13:26:31 | ERROR | ❌ Failed to process page 1: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FF862650>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245DD421A30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8B240>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245DD421A30>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 47, in scrape_story_hierarchy
    pages_data = await self._process_pages(story_data, request, max_pages)
                       │    │              │           │        └ 100
                       │    │              │           └ <starlette.requests.Request object at 0x00000245DD421A30>
                       │    │              └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                       │    └ <function HierarchicalScrapingService._process_pages at 0x00000245E008CAE0>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 165, in _process_pages
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:26:31 | INFO | 💾 Phase 3: Storing hierarchy in database
2025-07-14 13:26:32 | ERROR | Failed to store hierarchy: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3170efd75d8e6139eb3'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999285), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999388), 'updated_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999390), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FF862650>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245DD421A30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8B240>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245DD421A30>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 51, in scrape_story_hierarchy
    result = await self._store_hierarchy(story_data, pages_data)
                   │    │                │           └ []
                   │    │                └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                   │    └ <function HierarchicalScrapingService._store_hierarchy at 0x00000245E008CB80>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 201, in _store_hierarchy
    story_result = await self.db.stories.replace_one(
                         │    └ AsyncIOMotorDatabase(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, drive...
                         └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1217, in replace_one
    self._update_retryable(
    │    └ <function Collection._update_retryable at 0x00000245DD76A660>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1118, in _update_retryable
    return self._database.client._retryable_write(
           │    │         └ <property object at 0x00000245DD79C860>
           │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.UPDATE: 'update'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245FFD7EE70>
           │    │                   │          └ <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x00000245DD7D6A20>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x00000245DD7D6B60>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>, 'session': <pymongo.synchronous.clie...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x00000245DD7D6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x00000245DD7D8180>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    └ <function _ClientConnectionRetryable._read at 0x00000245DD7D8220>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245FFD7EE70>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    └ <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1098, in _update
    return self._update(
           │    └ <function Collection._update at 0x00000245DD76A5C0>
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1049, in _update
    conn.command(
    │    └ <function _handle_reauth.<locals>.inner at 0x00000245DD7AF420>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\helpers.py", line 47, in inner
    return func(*args, **kwargs)
           │     │       └ {'write_concern': WriteConcern(), 'codec_options': CodecOptions(document_class=dict, tz_aware=False, uuid_representation=Uuid...
           │     └ (Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272, 'webtruyen_api...
           └ <function Connection.command at 0x00000245DD7AF380>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 442, in command
    self._raise_connection_failure(error)
    │    └ <function Connection._raise_connection_failure at 0x00000245DD7AFE20>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 414, in command
    return command(
           └ <function command at 0x00000245DD7AEC00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\network.py", line 148, in command
    request_id, msg, size, max_doc_size = message._op_msg(
                                          │       └ <function _op_msg at 0x00000245DCCE3F60>
                                          └ <module 'pymongo.message' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymongo\\message....
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\message.py", line 419, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
           │                    │      │        │           │     └ CodecOptions(document_class=dict, tz_aware=False, uuid_representation=UuidRepresentation.UNSPECIFIED, unicode_decode_error_ha...
           │                    │      │        │           └ [{'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truy...
           │                    │      │        └ 'updates'
           │                    │      └ {'update': 'stories', 'ordered': True, 'lsid': {'id': Binary(b'\xbe&\xcb\xc7\r.L\x94\xb6\xfa9\x9c\x8b&\x8f;', 4)}, '$db': 'we...
           │                    └ 0
           └ <built-in function _op_msg>

bson.errors.InvalidDocument: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3170efd75d8e6139eb3'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999285), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999388), 'updated_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999390), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
2025-07-14 13:26:32 | ERROR | ❌ Hierarchical scraping failed: Database storage failed: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3170efd75d8e6139eb3'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999285), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999388), 'updated_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999390), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 201, in _store_hierarchy
    story_result = await self.db.stories.replace_one(
                         │    └ AsyncIOMotorDatabase(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, drive...
                         └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1217, in replace_one
    self._update_retryable(
    │    └ <function Collection._update_retryable at 0x00000245DD76A660>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1118, in _update_retryable
    return self._database.client._retryable_write(
           │    │         └ <property object at 0x00000245DD79C860>
           │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.UPDATE: 'update'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245FFD7EE70>
           │    │                   │          └ <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x00000245DD7D6A20>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x00000245DD7D6B60>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>, 'session': <pymongo.synchronous.clie...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x00000245DD7D6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x00000245DD7D8180>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    └ <function _ClientConnectionRetryable._read at 0x00000245DD7D8220>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245FFD7EE70>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
           │    └ <function Collection._update_retryable.<locals>._update at 0x000002458881E0C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FFAF2BA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1098, in _update
    return self._update(
           │    └ <function Collection._update at 0x00000245DD76A5C0>
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1049, in _update
    conn.command(
    │    └ <function _handle_reauth.<locals>.inner at 0x00000245DD7AF420>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\helpers.py", line 47, in inner
    return func(*args, **kwargs)
           │     │       └ {'write_concern': WriteConcern(), 'codec_options': CodecOptions(document_class=dict, tz_aware=False, uuid_representation=Uuid...
           │     └ (Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272, 'webtruyen_api...
           └ <function Connection.command at 0x00000245DD7AF380>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 442, in command
    self._raise_connection_failure(error)
    │    └ <function Connection._raise_connection_failure at 0x00000245DD7AFE20>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFA24190>) CLOSED at 2499664824272
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 414, in command
    return command(
           └ <function command at 0x00000245DD7AEC00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\network.py", line 148, in command
    request_id, msg, size, max_doc_size = message._op_msg(
                                          │       └ <function _op_msg at 0x00000245DCCE3F60>
                                          └ <module 'pymongo.message' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymongo\\message....
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\message.py", line 419, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
           │                    │      │        │           │     └ CodecOptions(document_class=dict, tz_aware=False, uuid_representation=UuidRepresentation.UNSPECIFIED, unicode_decode_error_ha...
           │                    │      │        │           └ [{'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truy...
           │                    │      │        └ 'updates'
           │                    │      └ {'update': 'stories', 'ordered': True, 'lsid': {'id': Binary(b'\xbe&\xcb\xc7\r.L\x94\xb6\xfa9\x9c\x8b&\x8f;', 4)}, '$db': 'we...
           │                    └ 0
           └ <built-in function _op_msg>

bson.errors.InvalidDocument: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3170efd75d8e6139eb3'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999285), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999388), 'updated_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999390), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000245FFA8ADE0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FF862650>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8AD40>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245DD421A30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000245FFA8B240>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000245FFA8AAC0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000245FFA8AF20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245DD421A30>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 51, in scrape_story_hierarchy
    result = await self._store_hierarchy(story_data, pages_data)
                   │    │                │           └ []
                   │    │                └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                   │    └ <function HierarchicalScrapingService._store_hierarchy at 0x00000245E008CB80>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 252, in _store_hierarchy
    raise ScrapingError(f"Database storage failed: {str(e)}")
          └ <class 'API.middleware.error_handling.ScrapingError'>

API.middleware.error_handling.ScrapingError: Database storage failed: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3170efd75d8e6139eb3'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999285), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999388), 'updated_at': datetime.datetime(2025, 7, 14, 6, 26, 31, 999390), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
2025-07-14 13:26:32 | INFO | ⚠️ POST /api/v1/hierarchical/scrape - Status: 400 - Time: 11.356s - Size: 2885
2025-07-14 13:27:31 | INFO | 🔵 POST /api/v1/hierarchical/scrape - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-14 13:27:31 | INFO | 🚀 Phase 1: Scraping story info from https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/
2025-07-14 13:27:32 | INFO | 🚀 Scraping story info from: https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/
2025-07-14 13:27:32 | INFO | 📄 Navigating to page...
2025-07-14 13:27:36 | INFO | ✅ Page loaded successfully
2025-07-14 13:27:36 | INFO | 💾 HTML saved to: D:\Personal Projects\Vibe\Webtruyen\BE\Scraper\html_debug\_truyen_phan_phai_tieng_long_bi_nu_chinh_nghe_len_ve_sau_noi_dung_cot_truyen_sap_.html
2025-07-14 13:27:36 | INFO | 🔍 Extracting story info using strategy...
2025-07-14 13:27:36 | INFO | 🔍 WebtruyenStrategy config keys: ['base_url', 'selectors', 'wait_conditions']
2025-07-14 13:27:36 | INFO | 🔍 Selectors loaded: 16 items - ['content', 'title', 'next_chapter', 'prev_chapter', 'chapter_list', 'chapter_link', 'story_title', 'story_image', 'story_author', 'story_description', 'meta_description', 'meta_keywords', 'story_chapters', 'story_pagination', 'story_pagination_link', 'locked_content']
2025-07-14 13:27:36 | INFO | 🔍 Extracting title with selectors: {'content': '.chapter-content, .story-content, .content-area p', 'title': 'h1, .entry-title, .chapter-title, h3', 'next_chapter': 'a[id="next-link"], a[href*="chuong-"]:contains("Sau"), .nav-next a', 'prev_chapter': 'a[id="prev-link"], a[href*="chuong-"]:contains("Trước"), .nav-previous a', 'chapter_list': '#chapter-list-tab, .chapter-list, .story-chapters', 'chapter_link': 'a[class*="uk-link-toggle"]', 'story_title': '#category-title', 'story_image': 'img[src^="https://i0.wp"]', 'story_author': 'div:contains("Tác giả") + a[href*="tac-gia"]', 'story_description': 'div.hide-long-text.uk-position-relative.uk-text-justify', 'meta_description': 'meta[name="description"]', 'meta_keywords': 'meta[name="keywords"]', 'story_chapters': '#chapter-list-tab, .chapter-list, .story-chapters', 'story_pagination': 'ul.uk-pagination.uk-flex-center', 'story_pagination_link': 'ul.uk-pagination.uk-flex-center a', 'locked_content': '.premium-content, .vip-content, .locked-content, .paywall'}
2025-07-14 13:27:36 | INFO | 📝 Trying story_title selector: #category-title
2025-07-14 13:27:36 | INFO | ✅ Found title: 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập'
2025-07-14 13:27:36 | INFO | ✅ Found description: 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?

X...'
2025-07-14 13:27:36 | INFO | ✅ Found cover image: 'https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'
2025-07-14 13:27:36 | WARNING | ❌ No author found with selector: div:contains("Tác giả") + a[href*="tac-gia"]
2025-07-14 13:27:36 | INFO | ℹ️ No story_total_chapters selector configured
2025-07-14 13:27:37 | INFO | 📖 Story info extracted: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:27:37 | INFO | 📊 Story info extracted: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:27:37 | INFO | 📖 Title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập
2025-07-14 13:27:37 | INFO | 👤 Author: Unknown
2025-07-14 13:27:37 | INFO | 📝 Description: Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?

X...
2025-07-14 13:27:37 | WARNING | Missing required field 'timestamp' in data
2025-07-14 13:27:37 | WARNING | Missing required field 'timestamp' in data
2025-07-14 13:27:37 | ERROR | Failed to extract total chapters: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FFA5A7A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245FFA5B240>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881E2A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245FFA5B240>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 43, in scrape_story_hierarchy
    story_data = await self._scrape_story_info(story_url, request)
                       │    │                  │          └ <starlette.requests.Request object at 0x00000245FFA5B240>
                       │    │                  └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                       │    └ <function HierarchicalScrapingService._scrape_story_info at 0x00000245E008C900>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 73, in _scrape_story_info
    total_chapters = await self._extract_total_chapters(story_info, request, story_url)
                           │    │                       │           │        └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                           │    │                       │           └ <starlette.requests.Request object at 0x00000245FFA5B240>
                           │    │                       └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                           │    └ <function HierarchicalScrapingService._extract_total_chapters at 0x00000245E008C9A0>
                           └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 100, in _extract_total_chapters
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:27:37 | ERROR | Failed to generate page URLs: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FFA5A7A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245FFA5B240>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881E2A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245FFA5B240>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 43, in scrape_story_hierarchy
    story_data = await self._scrape_story_info(story_url, request)
                       │    │                  │          └ <starlette.requests.Request object at 0x00000245FFA5B240>
                       │    │                  └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                       │    └ <function HierarchicalScrapingService._scrape_story_info at 0x00000245E008C900>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 76, in _scrape_story_info
    page_urls = await self._generate_page_urls(story_url, request)
                      │    │                   │          └ <starlette.requests.Request object at 0x00000245FFA5B240>
                      │    │                   └ 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'
                      │    └ <function HierarchicalScrapingService._generate_page_urls at 0x00000245E008CA40>
                      └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 133, in _generate_page_urls
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:27:37 | INFO | 📄 Phase 2: Processing 1 pages
2025-07-14 13:27:37 | INFO | 📄 Processing page 1/1: https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/
2025-07-14 13:27:37 | ERROR | ❌ Failed to process page 1: 'MetruyenScraper' object has no attribute 'browser_manager'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FFA5A7A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245FFA5B240>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881E2A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245FFA5B240>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 47, in scrape_story_hierarchy
    pages_data = await self._process_pages(story_data, request, max_pages)
                       │    │              │           │        └ 100
                       │    │              │           └ <starlette.requests.Request object at 0x00000245FFA5B240>
                       │    │              └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                       │    └ <function HierarchicalScrapingService._process_pages at 0x00000245E008CAE0>
                       └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 165, in _process_pages
    page = await scraper.browser_manager.new_page()
                 └ <src.metruyenscraper.MetruyenScraper object at 0x00000245FF7DEBA0>

AttributeError: 'MetruyenScraper' object has no attribute 'browser_manager'
2025-07-14 13:27:37 | INFO | 💾 Phase 3: Storing hierarchy in database
2025-07-14 13:27:37 | ERROR | Failed to store hierarchy: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3590efd75d8e6139eb4'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40340), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40415), 'updated_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40416), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FFA5A7A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245FFA5B240>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881E2A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245FFA5B240>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 51, in scrape_story_hierarchy
    result = await self._store_hierarchy(story_data, pages_data)
                   │    │                │           └ []
                   │    │                └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                   │    └ <function HierarchicalScrapingService._store_hierarchy at 0x00000245E008CB80>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 201, in _store_hierarchy
    story_result = await self.db.stories.replace_one(
                         │    └ AsyncIOMotorDatabase(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, drive...
                         └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1217, in replace_one
    self._update_retryable(
    │    └ <function Collection._update_retryable at 0x00000245DD76A660>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1118, in _update_retryable
    return self._database.client._retryable_write(
           │    │         └ <property object at 0x00000245DD79C860>
           │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.UPDATE: 'update'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245898D19A0>
           │    │                   │          └ <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x00000245DD7D6A20>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x00000245DD7D6B60>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>, 'session': <pymongo.synchronous.clie...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x00000245DD7D6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x00000245DD7D8180>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    └ <function _ClientConnectionRetryable._read at 0x00000245DD7D8220>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245898D19A0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    └ <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1098, in _update
    return self._update(
           │    └ <function Collection._update at 0x00000245DD76A5C0>
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1049, in _update
    conn.command(
    │    └ <function _handle_reauth.<locals>.inner at 0x00000245DD7AF420>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\helpers.py", line 47, in inner
    return func(*args, **kwargs)
           │     │       └ {'write_concern': WriteConcern(), 'codec_options': CodecOptions(document_class=dict, tz_aware=False, uuid_representation=Uuid...
           │     └ (Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592, 'webtruyen_api...
           └ <function Connection.command at 0x00000245DD7AF380>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 442, in command
    self._raise_connection_failure(error)
    │    └ <function Connection._raise_connection_failure at 0x00000245DD7AFE20>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 414, in command
    return command(
           └ <function command at 0x00000245DD7AEC00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\network.py", line 148, in command
    request_id, msg, size, max_doc_size = message._op_msg(
                                          │       └ <function _op_msg at 0x00000245DCCE3F60>
                                          └ <module 'pymongo.message' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymongo\\message....
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\message.py", line 419, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
           │                    │      │        │           │     └ CodecOptions(document_class=dict, tz_aware=False, uuid_representation=UuidRepresentation.UNSPECIFIED, unicode_decode_error_ha...
           │                    │      │        │           └ [{'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truy...
           │                    │      │        └ 'updates'
           │                    │      └ {'update': 'stories', 'ordered': True, 'lsid': {'id': Binary(b'\xbe&\xcb\xc7\r.L\x94\xb6\xfa9\x9c\x8b&\x8f;', 4)}, '$db': 'we...
           │                    └ 0
           └ <built-in function _op_msg>

bson.errors.InvalidDocument: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3590efd75d8e6139eb4'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40340), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40415), 'updated_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40416), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
2025-07-14 13:27:37 | ERROR | ❌ Hierarchical scraping failed: Database storage failed: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3590efd75d8e6139eb4'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40340), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40415), 'updated_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40416), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 201, in _store_hierarchy
    story_result = await self.db.stories.replace_one(
                         │    └ AsyncIOMotorDatabase(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, drive...
                         └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1217, in replace_one
    self._update_retryable(
    │    └ <function Collection._update_retryable at 0x00000245DD76A660>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1118, in _update_retryable
    return self._database.client._retryable_write(
           │    │         └ <property object at 0x00000245DD79C860>
           │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.UPDATE: 'update'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245898D19A0>
           │    │                   │          └ <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x00000245DD7D6A20>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x00000245DD7D6B60>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>, 'session': <pymongo.synchronous.clie...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x00000245DD7D6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x00000245DD7D8180>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    └ <function _ClientConnectionRetryable._read at 0x00000245DD7D8220>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x00000245898D19A0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
           │    └ <function Collection._update_retryable.<locals>._update at 0x000002458881EAC0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x00000245FF839450>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1098, in _update
    return self._update(
           │    └ <function Collection._update at 0x00000245DD76A5C0>
           └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 1049, in _update
    conn.command(
    │    └ <function _handle_reauth.<locals>.inner at 0x00000245DD7AF420>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\helpers.py", line 47, in inner
    return func(*args, **kwargs)
           │     │       └ {'write_concern': WriteConcern(), 'codec_options': CodecOptions(document_class=dict, tz_aware=False, uuid_representation=Uuid...
           │     └ (Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592, 'webtruyen_api...
           └ <function Connection.command at 0x00000245DD7AF380>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 442, in command
    self._raise_connection_failure(error)
    │    └ <function Connection._raise_connection_failure at 0x00000245DD7AFE20>
    └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x00000245FFDA8050>) CLOSED at 2499664824592
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\pool.py", line 414, in command
    return command(
           └ <function command at 0x00000245DD7AEC00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\network.py", line 148, in command
    request_id, msg, size, max_doc_size = message._op_msg(
                                          │       └ <function _op_msg at 0x00000245DCCE3F60>
                                          └ <module 'pymongo.message' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pymongo\\message....
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\message.py", line 419, in _op_msg
    return _op_msg_uncompressed(flags, command, identifier, docs, opts)
           │                    │      │        │           │     └ CodecOptions(document_class=dict, tz_aware=False, uuid_representation=UuidRepresentation.UNSPECIFIED, unicode_decode_error_ha...
           │                    │      │        │           └ [{'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truy...
           │                    │      │        └ 'updates'
           │                    │      └ {'update': 'stories', 'ordered': True, 'lsid': {'id': Binary(b'\xbe&\xcb\xc7\r.L\x94\xb6\xfa9\x9c\x8b&\x8f;', 4)}, '$db': 'we...
           │                    └ 0
           └ <built-in function _op_msg>

bson.errors.InvalidDocument: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3590efd75d8e6139eb4'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40340), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40415), 'updated_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40416), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\main.py", line 19, in <module>
    uvicorn.run("API.main:app", host="0.0.0.0", port=8000, reload=False)
    │       └ <function run at 0x00000245DB2EFEC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000245DB32EF20>
    └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000245DB32EFC0>
           │       │   └ <uvicorn.server.Server object at 0x00000245FF30E7B0>
           │       └ <function run at 0x00000245DA7BFCE0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000245DD5B74C0>
           │      └ <function Runner.run at 0x00000245DA812200>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000245DA817D80>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000245FF30EA50>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000245DA817CE0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000245DA811B20>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000245DA74C860>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
          └ <API.middleware.error_handling.ErrorHandlingMiddleware object at 0x00000245FF7DD940>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '184', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002458881CEA0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000245DBB68CC0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000245FF7DD7F0...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000245FFA5A7A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000245FF7DD6A0>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000245DD65DA90>>
          └ <fastapi.routing.APIRouter object at 0x00000245DD65DA90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000245DBAE02C0>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000245FF844860>
          └ APIRoute(path='/api/v1/hierarchical/scrape', name='scrape_story_hierarchical', methods=['POST'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881CCC0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000245FFA5B240>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
          └ <function wrap_app_handling_exceptions at 0x00000245DBAB6AC0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002458881E2A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002458881CFE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002458881D260>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000245FFA5B240>
                     └ <function get_request_handler.<locals>.app at 0x00000245FF8447C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000245DBAB62A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'service': <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>, 'request_d...
                 │         └ <function scrape_story_hierarchical at 0x00000245E008C2C0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\hierarchical_scraping.py", line 91, in scrape_story_hierarchical
    result = await service.scrape_story_hierarchy(
                   │       └ <function HierarchicalScrapingService.scrape_story_hierarchy at 0x00000245E008C860>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 51, in scrape_story_hierarchy
    result = await self._store_hierarchy(story_data, pages_data)
                   │    │                │           └ []
                   │    │                └ {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/...
                   │    └ <function HierarchicalScrapingService._store_hierarchy at 0x00000245E008CB80>
                   └ <API.services.hierarchical_scraping_service.HierarchicalScrapingService object at 0x00000245FFA29D30>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\services\hierarchical_scraping_service.py", line 252, in _store_hierarchy
    raise ScrapingError(f"Database storage failed: {str(e)}")
          └ <class 'API.middleware.error_handling.ScrapingError'>

API.middleware.error_handling.ScrapingError: Database storage failed: Invalid document {'q': {'url': 'https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'}, 'u': {'_id': ObjectId('6874a3590efd75d8e6139eb4'), 'title': 'Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập', 'url': HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), 'slug': None, 'total_pages': 0, 'total_pages_scraped': 0, 'total_chapters_scraped': 0, 'total_chapters_enhanced': 0, 'scraping_status': <ScrapingStatus.COMPLETED: 'completed'>, 'enhancement_progress': 0.0, 'metadata': {'author': 'Unknown', 'description': 'Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?\n\nXuyên qua thành hẳn phải chết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thánh nữ lâm vào trầm tư.\n\nChỉ cần theo hệ thống yêu cầu đi đến nhiệm vụ dây, liền có thể mang theo chục tỷ tài sản cùng vĩnh sinh trở lại địa cầu, có thể đỉnh đầu đột nhiên toát ra bọt khí khung là chuyện gì xảy ra?\n\n“Cái này dây thừng trói thật chát chát. . . Nam chính nửa giờ mới đến đủ ta tạo ba cái em bé “\n\n“Nữ chính đừng cọ xát! Lại cọ ta thật muốn biến cầm thú a “\n\nCố Thanh Hàn nhìn chằm chằm điên cuồng đậu đen rau muống bọt khí, tiên khu chấn động.\n\nNàng đột nhiên phát hiện: Cái kia cường cưới mình hoàn khố, đúng là đang dùng sinh mệnh thả biển diễn phản phái?\n\nLâm Bạch nhìn xem tập thể băng nhân thiết kịch bản điên rồi:\n\nĐã nói xong ngược phản phái đâu? Làm sao toàn thành ta Tu La tràng!', 'genres': [], 'tags': [], 'cover_image_url': HttpUrl('https://i0.wp.com/webtruyen.diendantruyen.com/wp-content/uploads/2025/03/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap.webp?resize=300%2C405&ssl=1'), 'status': <StoryStatus.ONGOING: 'ongoing'>, 'total_chapters': 0, 'total_pages': 0, 'scraped_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40340), 'scraping_duration': None, 'source_website': 'webtruyen', 'scraping_errors': []}, 'created_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40415), 'updated_at': datetime.datetime(2025, 7, 14, 6, 27, 37, 40416), 'last_scraped_at': None}, 'multi': False, 'upsert': True} | cannot encode object: HttpUrl('https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/'), of type: <class 'pydantic.networks.HttpUrl'>
2025-07-14 13:27:37 | INFO | ⚠️ POST /api/v1/hierarchical/scrape - Status: 400 - Time: 5.102s - Size: 2882
2025-07-14 13:34:01 | INFO | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-14 13:34:08 | INFO | MetruyenScraper initialized
2025-07-14 13:34:08 | INFO | ✅ Scraper configured
2025-07-14 13:34:08 | INFO | Initializing AI enhancement service...
2025-07-14 13:34:08 | INFO | Initialized Gemini model: gemini-2.5-flash
2025-07-14 13:34:08 | INFO | ✅ AI enhancement service initialized successfully
2025-07-14 13:34:08 | INFO | ✅ Enhancement service initialized
2025-07-14 13:34:08 | INFO | ✅ Application startup completed
2025-07-14 13:36:11 | INFO | MetruyenScraper initialized
2025-07-14 13:36:11 | INFO | ✅ Scraper configured
2025-07-14 13:36:11 | INFO | Initializing AI enhancement service...
2025-07-14 13:36:11 | INFO | Initialized Gemini model: gemini-2.5-flash
2025-07-14 13:36:11 | INFO | ✅ AI enhancement service initialized successfully
2025-07-14 13:36:11 | INFO | ✅ Enhancement service initialized
2025-07-14 13:36:11 | INFO | ✅ Application startup completed
