import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'Scraper', 'src'))

from config_manager import Config<PERSON>anager

def debug_config():
    print("🔍 Debugging ConfigManager...")
    
    # Initialize ConfigManager
    config_manager = ConfigManager('Scraper/config.yaml')
    
    print(f"📄 Config file loaded: {config_manager.config_path}")
    
    # Check if targets exist
    targets = config_manager.get('targets', {})
    print(f"🎯 Available targets: {list(targets.keys())}")
    
    # Check webtruyen target
    if 'webtruyen' in targets:
        webtruyen_config = config_manager.get_target_config('webtruyen')
        print(f"🌐 Webtruyen config keys: {list(webtruyen_config.keys())}")
        
        # Check selectors
        selectors = webtruyen_config.get('selectors', {})
        print(f"🔧 Selectors count: {len(selectors)}")
        print(f"🔧 Selector keys: {list(selectors.keys())}")
        
        # Print specific selectors
        important_selectors = ['story_author', 'chapter_list', 'story_pagination']
        for selector_name in important_selectors:
            if selector_name in selectors:
                print(f"✅ {selector_name}: {selectors[selector_name]}")
            else:
                print(f"❌ {selector_name}: NOT FOUND")
    else:
        print("❌ Webtruyen target not found!")

if __name__ == "__main__":
    debug_config()