'use client';

import { useEffect, useState } from 'react';
import { fetchStories } from '@/services/storyService';
import { Story } from '@/types/story';
import StoryCard from './StoryCard';

const StoryList = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const loadStories = async (pageNum: number = 1) => {
    try {
      setLoading(true);
      const response = await fetchStories(pageNum, 12);
      
      if (pageNum === 1) {
          setStories(response.data);
        } else {
          setStories(prev => [...prev, ...response.data]);
        }
        
        setHasMore(response.pagination.has_next);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStories(1);
  }, []);

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadStories(nextPage);
    }
  };

  if (loading && stories.length === 0) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="bg-zinc-800/50 rounded-lg overflow-hidden animate-pulse">
            <div className="aspect-[3/4] bg-zinc-700"></div>
            <div className="p-4">
              <div className="h-6 bg-zinc-700 rounded mb-2"></div>
              <div className="h-4 bg-zinc-700 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-zinc-700 rounded mb-3 w-1/2"></div>
              <div className="flex justify-between">
                <div className="h-3 bg-zinc-700 rounded w-16"></div>
                <div className="h-3 bg-zinc-700 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center mt-8">
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-6">
          <h3 className="text-red-400 font-semibold mb-2">Lỗi tải dữ liệu</h3>
          <p className="text-red-300 mb-4">{error}</p>
          <button
            onClick={() => fetchStories(1)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  if (stories.length === 0) {
    return (
      <div className="text-center mt-8">
        <div className="bg-zinc-800/50 border border-zinc-700 rounded-lg p-8">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
          </svg>
          <h3 className="text-gray-300 font-semibold mb-2">Chưa có truyện nào</h3>
          <p className="text-gray-400 mb-4">Hãy bắt đầu bằng cách cào một truyện mới!</p>
          <a
            href="/scrape"
            className="inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            Cào truyện mới
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {stories.map((story) => (
          <StoryCard key={story.id} story={story} />
        ))}
      </div>
      
      {hasMore && (
        <div className="text-center mt-8">
          <button
            onClick={loadMore}
            disabled={loading}
            className="bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-md transition-colors"
          >
            {loading ? 'Đang tải...' : 'Tải thêm'}
          </button>
        </div>
      )}
    </div>
  );
};

export default StoryList;