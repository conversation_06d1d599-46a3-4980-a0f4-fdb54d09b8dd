# d:\Personal Projects\Vibe\Webtruyen\BE\Scraper\src\browser_manager.py

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Playwright

class BrowserManager:
    """Manages the Playwright browser instance."""
    def __init__(self, config: dict):
        self.config = config
        self.playwright: Playwright = None
        self.browser: Browser = None

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    async def start(self):
        """Starts the browser and applies configurations."""
        self.playwright = await async_playwright().start()
        browser_config = self.config.get('scraper', {}).get('browser', {})
        launch_options = {
            'headless': browser_config.get('headless', True),
            'args': browser_config.get('args', [])
        }
        
        # Check if custom executable path is specified and exists
        executable_path = browser_config.get('executable_path')
        if executable_path:
            import os
            if os.path.exists(executable_path):
                launch_options['executable_path'] = executable_path
        
        self.browser = await self.playwright.chromium.launch(**launch_options)

    async def stop(self):
        """Stops the browser and the Playwright instance."""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def new_page(self) -> Page:
        """Creates a new page in the browser."""
        if not self.browser:
            raise RuntimeError("Browser is not started.")
        return await self.browser.new_page()