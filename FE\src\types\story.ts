export type StoryStatus = 'ongoing' | 'completed' | 'hiatus' | 'dropped' | 'unknown';

export interface Story {
  id: string;
  title: string;
  author?: string;
  description?: string;
  cover_image_url?: string;
  status: 'ongoing' | 'completed' | 'hiatus' | 'dropped' | 'unknown';
  total_chapters_scraped: number;
  total_chapters_enhanced: number;
  enhancement_progress: number;
  created_at: string;
  updated_at: string;
  url?: string;
  genres?: string[];
  tags?: string[];
  scraping_status?: string;
  last_scraped_at?: string;
}

export interface Chapter {
  id: string;
  story_id: string;
  chapter_number: number;
  title: string;
  url: string;
  original_content?: string;
  enhanced_content?: string;
  is_scraped: boolean;
  is_enhanced: boolean;
  enhancement_status: string;
  word_count?: number;
  created_at: string;
  updated_at: string;
  enhancement_metadata?: any;
}

export interface PaginationInfo {
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface APIResponse<T> {
  status: 'success' | 'error';
  message: string;
  data: T;
}

export interface PaginatedAPIResponse<T> extends APIResponse<T[]> {
  pagination: PaginationInfo;
}

export interface StoryWithChapters {
  id: string;
  title: string;
  url: string;
  author?: string;
  description?: string;
  genres: string[];
  tags: string[];
  status: StoryStatus;
  cover_image_url?: string;
  total_chapters_scraped: number;
  total_chapters_enhanced: number;
  enhancement_progress: number;
  scraping_status: string;
  created_at: string;
  updated_at: string;
  last_scraped_at?: string;
}

export type StoriesResponse = PaginatedAPIResponse<Story>;

export type ChaptersResponse = PaginatedAPIResponse<Chapter>;