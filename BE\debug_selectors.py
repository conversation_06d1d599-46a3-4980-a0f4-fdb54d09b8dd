#!/usr/bin/env python3
"""
Debug script to test selectors and save HTML
"""

import asyncio
import os
from playwright.async_api import async_playwright
from urllib.parse import urlparse

async def debug_selectors():
    url = "https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        print(f"🚀 Navigating to: {url}")
        await page.goto(url, wait_until='networkidle')
        print(f"✅ Page loaded")
        
        # Save HTML
        html_content = await page.content()
        parsed_url = urlparse(url)
        filename = parsed_url.path.replace('/', '_').replace('-', '_')
        if not filename or filename == '_':
            filename = 'story_page'
        
        debug_dir = 'html_debug'
        os.makedirs(debug_dir, exist_ok=True)
        debug_file = os.path.join(debug_dir, f"{filename}.html")
        
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 HTML saved to: {debug_file}")
        
        # Test selectors
        selectors = {
            'story_title': '#category-title',
            'story_author': 'div:contains("Tác giả") + a[href*="tac-gia"]',
            'story_description': 'div.hide-long-text.uk-position-relative.uk-text-justify',
            'story_pagination': 'ul.uk-pagination.uk-flex-center',
            'chapter_list': '#chapter-list-tab, .chapter-list, .story-chapters',
            'chapter_link': 'a[class*="uk-link-toggle"]'
        }
        
        print("\n🔍 Testing selectors:")
        for name, selector in selectors.items():
            try:
                element = await page.query_selector(selector)
                if element:
                    if name == 'story_title':
                        text = await element.inner_text()
                        print(f"✅ {name}: Found - '{text.strip()}'")
                    elif name == 'story_author':
                        text = await element.inner_text()
                        print(f"✅ {name}: Found - '{text.strip()}'")
                    elif name == 'story_description':
                        text = await element.inner_text()
                        print(f"✅ {name}: Found - '{text.strip()[:100]}...'")
                    elif name == 'story_pagination':
                        links = await element.query_selector_all('a')
                        print(f"✅ {name}: Found - {len(links)} pagination links")
                        for i, link in enumerate(links[:5]):
                            link_text = await link.inner_text()
                            link_href = await link.get_attribute('href')
                            print(f"   Link {i+1}: '{link_text}' -> {link_href}")
                    elif name == 'chapter_list':
                        print(f"✅ {name}: Found container")
                    elif name == 'chapter_link':
                        all_links = await page.query_selector_all(selector)
                        print(f"✅ {name}: Found {len(all_links)} chapter links")
                        for i, link in enumerate(all_links[:5]):
                            link_text = await link.inner_text()
                            link_href = await link.get_attribute('href')
                            print(f"   Chapter {i+1}: '{link_text.strip()}' -> {link_href}")
                else:
                    print(f"❌ {name}: Not found")
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
        
        # Try alternative selectors
        print("\n🔍 Testing alternative selectors:")
        
        # Author alternatives
        author_selectors = [
            'div:contains("Tác giả") + a[href*="tac-gia"]',
            'a[href*="tac-gia"]',
            '.author',
            '[class*="author"]',
            'div:contains("Tác giả")',
            'span:contains("Tác giả")',
        ]
        
        for selector in author_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    text = await element.inner_text()
                    print(f"✅ Author alternative '{selector}': '{text.strip()}'")
                    break
            except:
                continue
        
        # Description alternatives
        desc_selectors = [
            'div.hide-long-text.uk-position-relative.uk-text-justify',
            '.hide-long-text',
            '[class*="description"]',
            '.content',
            '.summary',
            'meta[name="description"]'
        ]
        
        for selector in desc_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    if selector.startswith('meta'):
                        text = await element.get_attribute('content')
                    else:
                        text = await element.inner_text()
                    print(f"✅ Description alternative '{selector}': '{text.strip()[:100]}...'")
                    break
            except:
                continue
        
        # Pagination alternatives
        pagination_selectors = [
            'ul.uk-pagination.uk-flex-center',
            '.uk-pagination',
            '.pagination',
            '[class*="pagination"]',
            'nav[class*="pagination"]'
        ]
        
        for selector in pagination_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    links = await element.query_selector_all('a')
                    print(f"✅ Pagination alternative '{selector}': {len(links)} links")
                    break
            except:
                continue
        
        await browser.close()
        print(f"\n✅ Debug completed. Check {debug_file} for HTML structure.")

if __name__ == "__main__":
    asyncio.run(debug_selectors())