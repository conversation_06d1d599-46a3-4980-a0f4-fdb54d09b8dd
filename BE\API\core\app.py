"""
Vietnamese Web Novel Scraping and Enhancement API

A comprehensive FastAPI application for scraping Vietnamese web novels,
enhancing content with AI, and providing data retrieval APIs.

Features:
- Story information scraping
- Batch chapter content scraping with rate limiting
- AI-powered content enhancement using Google Gemini
- Data retrieval with pagination and filtering
- Job status tracking and progress monitoring
- Content comparison and export capabilities
"""

import logging
import sys
import os
from pathlib import Path
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, HTTPEx<PERSON>, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from API.models.database import db_manager
from API.services.scraping_service import ScrapingService
from API.services.enhancement_service import EnhancementService
from API.middleware.logging import LoggingMiddleware
from API.middleware.rate_limiting import RateLimitingMiddleware
from API.middleware.error_handling import ErrorHandlingMiddleware
from API.routers import stories, export, hierarchical_scraping
from API.routers.tasks import scraping, enhancement, jobs
from API.utils.logging_config import setup_logging

# Add scraper path to sys.path
scraper_path = Path(__file__).parent.parent.parent / "Scraper"
if str(scraper_path) not in sys.path:
    sys.path.insert(0, str(scraper_path))

from src.metruyenscraper import MetruyenScraper
from API.core.config import get_settings


async def ensure_scraper_started(app):
    """Ensure scraper is started before use"""
    scraper = app.state.scraper
    if not scraper.scraper_started:
        try:
            await scraper.start()
            logging.info("✅ Scraper started successfully")
        except Exception as e:
            logging.error(f"❌ Failed to start scraper: {e}")
            raise
    return scraper


# ============================================================================
# Application Lifecycle Management
# ============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    settings = get_settings()
    
    # Setup logging
    setup_logging(settings.log_level, settings.log_file)
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting Vietnamese Web Novel API...")
    
    # Connect to database
    try:
        await db_manager.connect(settings.mongodb_url)
        logger.info("✅ Database connection established")
    except Exception as e:
        logger.error(f"❌ Failed to connect to database: {e}")
        raise
    
    # Initialize background task manager
    app.state.background_tasks = set()

    # Initialize services
    try:
        config_path = Path(__file__).parent.parent.parent / "config.yaml"
        app.state.scraper = MetruyenScraper(str(config_path))
        logger.info("✅ Scraper configured")

        try:
            await EnhancementService.initialize()
            logger.info("✅ Enhancement service initialized")
        except Exception as enhancement_error:
            logger.warning(f"⚠️ Enhancement service not available: {enhancement_error}")
            logger.info("📝 Continuing without AI enhancement features")
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise

    logger.info("✅ Application startup completed")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Vietnamese Web Novel API...")
    
    # Cancel background tasks
    for task in app.state.background_tasks:
        if not task.done():
            task.cancel()
    
    # Cleanup services
    if hasattr(app.state, 'scraper') and app.state.scraper:
        await app.state.scraper.close()
    await EnhancementService.cleanup()
    logger.info("✅ Services cleaned up")

    # Disconnect from database
    await db_manager.disconnect()
    
    logger.info("✅ Application shutdown completed")


# ============================================================================
# FastAPI Application Setup
# ============================================================================

def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="Web Novel API",
        description=__doc__,
        version="1.0.0",
        docs_url="/docs",  # Always enable docs for development convenience
        redoc_url="/redoc",  # Always enable redoc for development convenience
        lifespan=lifespan
    )
    
    # ========================================================================
    # Middleware Configuration
    # ========================================================================
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins_list,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )

    # Trusted host middleware
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.allowed_hosts_list
        )
    
    # Custom middleware
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RateLimitingMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # ========================================================================
    # Exception Handlers
    # ========================================================================
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request, exc):
        """Handle request validation errors"""
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "message": "Validation error",
                "errors": exc.errors(),
                "timestamp": "2024-01-01T00:00:00Z"  # Will be set by middleware
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request, exc):
        """Handle HTTP exceptions"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "timestamp": "2024-01-01T00:00:00Z"  # Will be set by middleware
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        """Handle general exceptions"""
        logger = logging.getLogger(__name__)
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "Internal server error",
                "timestamp": "2024-01-01T00:00:00Z"  # Will be set by middleware
            }
        )
    
    # ========================================================================
    # Router Registration
    # ========================================================================
    
    # API v1 routes
    api_prefix = "/api/v1"
    
    app.include_router(
        stories.router,
        prefix=f"{api_prefix}/stories",
        tags=["Stories and Chapters"]
    )

    app.include_router(
        scraping.router,
        prefix=f"{api_prefix}/tasks/scraping",
        tags=["Scraping Tasks"]
    )

    app.include_router(
        enhancement.router,
        prefix=f"{api_prefix}/tasks/enhancement",
        tags=["Enhancement Tasks"]
    )

    app.include_router(
        jobs.router,
        prefix=f"{api_prefix}/tasks/jobs",
        tags=["Job Management"]
    )
    
    app.include_router(
        export.router,
        prefix=f"{api_prefix}/export",
        tags=["Data Export"]
    )
    
    app.include_router(
        hierarchical_scraping.router,
        prefix=f"{api_prefix}/hierarchical",
        tags=["Hierarchical Scraping"]
    )
    
    # ========================================================================
    # Health Check Endpoints
    # ========================================================================
    
    @app.get("/health", tags=["Health"])
    async def health_check():
        """Health check endpoint"""
        return {
            "success": True,
            "message": "API is healthy",
            "version": "1.0.0",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    
    @app.get("/health/database", tags=["Health"])
    async def database_health_check():
        """Database health check endpoint"""
        try:
            # Test database connection
            await db_manager.client.admin.command('ping')
            return {
                "success": True,
                "message": "Database connection is healthy",
                "timestamp": "2024-01-01T00:00:00Z"
            }
        except Exception as e:
            raise HTTPException(
                status_code=503,
                detail=f"Database connection failed: {str(e)}"
            )
    
    @app.get("/", tags=["Root"])
    async def root():
        """Root endpoint with API information"""
        return {
            "name": "Vietnamese Web Novel API",
            "version": "1.0.0",
            "description": "API for scraping and enhancing Vietnamese web novels",
            "docs_url": "/docs",
            "health_url": "/health",
            "api_prefix": "/api/v1"
        }
    
    return app


# ============================================================================
# Application Instance
# ============================================================================

app = create_app()


# ============================================================================
# Background Task Management
# ============================================================================

def add_background_task(task):
    """Add a background task to the application state"""
    app.state.background_tasks.add(task)
    task.add_done_callback(app.state.background_tasks.discard)


# ============================================================================
# Development Server
# ============================================================================

if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "API.core.app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        log_level=settings.log_level.lower(),
        access_log=True
    )
