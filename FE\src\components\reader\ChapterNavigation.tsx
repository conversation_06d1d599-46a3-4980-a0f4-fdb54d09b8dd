'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Chapter } from '@/types/story';

interface ChapterNavigationProps {
  storyId: string;
  currentChapter: Chapter;
  previousChapter: Chapter | null;
  nextChapter: Chapter | null;
  allChapters: Chapter[];
}

const ChapterNavigation = ({
  storyId,
  currentChapter,
  previousChapter,
  nextChapter,
  allChapters
}: ChapterNavigationProps) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const router = useRouter();

  const handleChapterSelect = (chapterNumber: number) => {
    router.push(`/stories/${storyId}/${chapterNumber}`);
    setIsDropdownOpen(false);
  };

  return (
    <div className="bg-zinc-800/50 rounded-lg p-4 mb-6 border border-zinc-700/50">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        {/* Previous Chapter Button */}
        <div className="flex-1">
          {previousChapter ? (
            <Link
              href={`/stories/${storyId}/${previousChapter.chapter_number}`}
              className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Chương trước
            </Link>
          ) : (
            <button 
              disabled 
              className="inline-flex items-center px-4 py-2 bg-zinc-700 text-gray-400 rounded-md cursor-not-allowed"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Chương trước
            </button>
          )}
        </div>

        {/* Chapter Selector */}
        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="inline-flex items-center px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md transition-colors"
          >
            <span>Chương {currentChapter.chapter_number}</span>
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isDropdownOpen && (
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-64 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
              {allChapters.map((chapter) => (
                <button
                  key={chapter.id}
                  onClick={() => handleChapterSelect(chapter.chapter_number)}
                  className={`w-full text-left px-4 py-2 hover:bg-zinc-700 transition-colors ${
                    chapter.id === currentChapter.id 
                      ? 'bg-indigo-600 text-white' 
                      : 'text-gray-300'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span>Chương {chapter.chapter_number}</span>
                    {chapter.id === currentChapter.id && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  {chapter.title && (
                    <div className="text-sm text-gray-400 truncate mt-1">
                      {chapter.title}
                    </div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Next Chapter Button */}
        <div className="flex-1 flex justify-end">
          {nextChapter ? (
            <Link
              href={`/stories/${storyId}/${nextChapter.chapter_number}`}
              className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors"
            >
              Chương sau
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          ) : (
            <button 
              disabled 
              className="inline-flex items-center px-4 py-2 bg-zinc-700 text-gray-400 rounded-md cursor-not-allowed"
            >
              Chương sau
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Back to Story Button */}
      <div className="mt-4 text-center">
        <Link
          href={`/stories/${storyId}`}
          className="inline-flex items-center px-3 py-1 text-sm bg-zinc-700 hover:bg-zinc-600 text-gray-300 hover:text-white rounded transition-colors"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Về trang truyện
        </Link>
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default ChapterNavigation;