import { fetchStoryById, fetchChapterByNumber, fetchChaptersByStoryId } from '@/services/storyService';
import ReaderView from '@/components/reader/ReaderView';
import ChapterNavigation from '@/components/reader/ChapterNavigation';
import { notFound } from 'next/navigation';

interface ReaderPageProps {
  params: {
    storyId: string;
    chapterId: string;
  };
}

export default async function ReaderPage({ params }: ReaderPageProps) {
  try {
    const chapterNumber = parseInt(params.chapterId);
  
  const [story, chapter, chaptersResponse] = await Promise.all([
     fetchStoryById(params.storyId),
     fetchChapterByNumber(params.storyId, chapterNumber),
     fetchChaptersByStoryId(params.storyId, 1, 1000) // Get all chapters for navigation
   ]);

    const currentChapterIndex = chaptersResponse.data.findIndex(
      (ch) => ch.chapter_number === chapterNumber
    );

    const previousChapter = currentChapterIndex > 0 ? chaptersResponse.data[currentChapterIndex - 1] : null;
    const nextChapter = currentChapterIndex < chaptersResponse.data.length - 1 ? chaptersResponse.data[currentChapterIndex + 1] : null;

    return (
      <div className="min-h-screen bg-zinc-900">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Story and Chapter Info */}
          <div className="mb-6">
            <nav className="text-sm text-gray-400 mb-4">
              <a href="/" className="hover:text-white transition-colors">Trang chủ</a>
              <span className="mx-2">/</span>
              <a href={`/stories/${params.storyId}`} className="hover:text-white transition-colors">
                {story.title}
              </a>
              <span className="mx-2">/</span>
              <span className="text-white">Chương {chapter.chapter_number}</span>
            </nav>
            
            <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700/50">
              <h1 className="text-2xl font-bold text-white mb-2">
                {story.title} - Chương {chapter.chapter_number}
              </h1>
              {chapter.title && (
                <h2 className="text-lg text-gray-300 mb-2">{chapter.title}</h2>
              )}
              <div className="flex items-center text-sm text-gray-400 space-x-4">
                <span>Tác giả: {story.author}</span>
                <span>{chapter.word_count?.toLocaleString() || 0} từ</span>
              <span>{new Date(chapter.updated_at).toLocaleDateString('vi-VN')}</span>
              </div>
            </div>
          </div>

          {/* Chapter Navigation - Top */}
          <ChapterNavigation
              storyId={params.storyId}
              currentChapter={chapter}
              previousChapter={previousChapter}
              nextChapter={nextChapter}
              allChapters={chaptersResponse.data}
            />

          {/* Chapter Content */}
          <ReaderView chapter={chapter} />

          {/* Chapter Navigation - Bottom */}
          <ChapterNavigation
            storyId={params.storyId}
            currentChapter={chapter}
            previousChapter={previousChapter}
            nextChapter={nextChapter}
            allChapters={chaptersResponse.data}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching chapter:', error);
    notFound();
  }
}

export async function generateMetadata({ params }: ReaderPageProps) {
  try {
    const chapterNumber = parseInt(params.chapterId);
    const [story, chapter] = await Promise.all([
      fetchStoryById(params.storyId),
      fetchChapterByNumber(params.storyId, chapterNumber)
    ]);
    
    const title = chapter.title 
      ? `${story.title} - Chương ${chapter.chapter_number}: ${chapter.title}`
        : `${story.title} - Chương ${chapter.chapter_number}`;
    
    return {
      title: `${title} - WebTruyen`,
      description: `Đọc ${title} của tác giả ${story.author}`,
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Chapter Not Found - WebTruyen',
    };
  }
}