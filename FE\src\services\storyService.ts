import { Story, Chapter, StoriesResponse, ChaptersResponse, StoryWithChapters } from '@/types/story';

const API_BASE_URL = 'http://localhost:8000';

// Fetch all stories with pagination
export const fetchStories = async (page: number = 1, pageSize: number = 20): Promise<StoriesResponse> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories?page=${page}&page_size=${pageSize}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch stories');
  }
  
  return response.json();
};

// Fetch a single story by ID
export const fetchStoryById = async (storyId: string): Promise<StoryWithChapters> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch story');
  }
  
  return response.json();
};

// Fetch chapters for a story
export const fetchChaptersByStoryId = async (
  storyId: string, 
  page: number = 1, 
  pageSize: number = 50
): Promise<ChaptersResponse> => {
  const response = await fetch(
    `${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters?page=${page}&page_size=${pageSize}`
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch chapters');
  }
  
  return response.json();
};

// Fetch a single chapter by chapter number
export const fetchChapterByNumber = async (storyId: string, chapterNumber: number): Promise<Chapter> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters/${chapterNumber}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch chapter');
  }
  
  return response.json();
};

// Fetch a single chapter by ID
export const fetchChapterById = async (chapterId: string): Promise<Chapter> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/stories/chapters/${chapterId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch chapter');
  }
  
  return response.json();
};