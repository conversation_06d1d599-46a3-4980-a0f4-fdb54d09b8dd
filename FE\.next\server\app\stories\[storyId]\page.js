/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/stories/[storyId]/page";
exports.ids = ["app/stories/[storyId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'stories',\n        {\n        children: [\n        '[storyId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/stories/[storyId]/page.tsx */ \"(rsc)/./src/app/stories/[storyId]/page.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/stories/[storyId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/stories/[storyId]/page\",\n        pathname: \"/stories/[storyId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CChapterList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CChapterList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/story/ChapterList.tsx */ \"(ssr)/./src/components/story/ChapterList.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1BlcnNvbmFsJTIwUHJvamVjdHMlNUMlNUNWaWJlJTVDJTVDV2VidHJ1eWVuJTVDJTVDRkUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDc3RvcnklNUMlNUNDaGFwdGVyTGlzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBcUk7QUFDckk7QUFDQSx3TEFBcUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8/MmFiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHN0b3J5XFxcXENoYXB0ZXJMaXN0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Ccomponents%5C%5Cstory%5C%5CChapterList.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUGVyc29uYWwlMjBQcm9qZWN0cyU1QyU1Q1ZpYmUlNUMlNUNXZWJ0cnV5ZW4lNUMlNUNGRSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8/NWZlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFBlcnNvbmFsIFByb2plY3RzXFxcXFZpYmVcXFxcV2VidHJ1eWVuXFxcXEZFXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQZXJzb25hbCUyMFByb2plY3RzJTVDJTVDVmliZSU1QyU1Q1dlYnRydXllbiU1QyU1Q0ZFJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvPzI4MWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQZXJzb25hbCBQcm9qZWN0c1xcXFxWaWJlXFxcXFdlYnRydXllblxcXFxGRVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CPersonal%20Projects%5C%5CVibe%5C%5CWebtruyen%5C%5CFE%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/story/ChapterList.tsx":
/*!**********************************************!*\
  !*** ./src/components/story/ChapterList.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _services_storyService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/storyService */ \"(ssr)/./src/services/storyService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ChapterList = ({ storyId, initialChapters, totalChapters })=>{\n    const [chapters, setChapters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialChapters);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialChapters.length < totalChapters);\n    const loadMoreChapters = async ()=>{\n        if (loading || !hasMore) return;\n        try {\n            setLoading(true);\n            const nextPage = page + 1;\n            const response = await (0,_services_storyService__WEBPACK_IMPORTED_MODULE_3__.fetchChaptersByStoryId)(storyId, nextPage, 50);\n            setChapters((prev)=>[\n                    ...prev,\n                    ...response.data\n                ]);\n            setPage(nextPage);\n            setHasMore(response.pagination.has_next);\n        } catch (error) {\n            console.error(\"Error loading more chapters:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-zinc-800/50 rounded-lg p-6 border border-zinc-700/50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Danh s\\xe1ch chương\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: [\n                            totalChapters,\n                            \" chương\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            chapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Chưa c\\xf3 chương n\\xe0o được tải\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\",\n                        children: chapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: `/stories/${storyId}/${chapter.chapter_number}`,\n                                className: \"group block bg-zinc-700/50 hover:bg-zinc-700 rounded-lg p-4 transition-all duration-200 border border-zinc-600/50 hover:border-indigo-500/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-white group-hover:text-indigo-400 transition-colors truncate\",\n                                                    children: [\n                                                        \"Chương \",\n                                                        chapter.chapter_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                chapter.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400 mt-1 truncate\",\n                                                    children: chapter.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mt-2 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                (chapter.word_count || 0).toLocaleString(),\n                                                                \" từ\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: new Date(chapter.updated_at).toLocaleDateString(\"vi-VN\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-1\",\n                                                    children: [\n                                                        chapter.is_scraped && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-green-400 bg-green-900/30 px-2 py-1 rounded\",\n                                                            children: \"Đ\\xe3 c\\xe0o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        chapter.is_enhanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-blue-400 bg-blue-900/30 px-2 py-1 rounded\",\n                                                            children: \"Đ\\xe3 n\\xe2ng cấp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-400 group-hover:text-indigo-400 transition-colors ml-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, chapter.id, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMoreChapters,\n                            disabled: loading,\n                            className: \"bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-md transition-colors\",\n                            children: loading ? \"Đang tải...\" : \"Tải th\\xeam chương\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\ChapterList.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChapterList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/story/ChapterList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/storyService.ts":
/*!**************************************!*\
  !*** ./src/services/storyService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchChapterById: () => (/* binding */ fetchChapterById),\n/* harmony export */   fetchChapterByNumber: () => (/* binding */ fetchChapterByNumber),\n/* harmony export */   fetchChaptersByStoryId: () => (/* binding */ fetchChaptersByStoryId),\n/* harmony export */   fetchStories: () => (/* binding */ fetchStories),\n/* harmony export */   fetchStoryById: () => (/* binding */ fetchStoryById)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\";\n// Fetch all stories with pagination\nconst fetchStories = async (page = 1, pageSize = 20)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch stories\");\n    }\n    return response.json();\n};\n// Fetch a single story by ID\nconst fetchStoryById = async (storyId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch story\");\n    }\n    return response.json();\n};\n// Fetch chapters for a story\nconst fetchChaptersByStoryId = async (storyId, page = 1, pageSize = 50)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapters\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by chapter number\nconst fetchChapterByNumber = async (storyId, chapterNumber)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters/${chapterNumber}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by ID\nconst fetchChapterById = async (chapterId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/chapters/${chapterId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/storyService.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3a1b96827cf5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzk5ODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzYTFiOTY4MjdjZjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Navbar */ \"(rsc)/./src/components/layout/Navbar.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"WebTruyen - Your Story Hub\",\n    description: \"A modern web application for scraping and reading stories.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} flex flex-col h-full bg-zinc-900 text-white`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-grow\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUppQjtBQUN5QjtBQUNBO0FBSXpDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFViwrSkFBZSxDQUFDLDRDQUE0QyxDQUFDOzs4QkFDL0UsOERBQUNDLGlFQUFNQTs7Ozs7OEJBQ1AsOERBQUNXO29CQUFLRixXQUFVOzhCQUFhSDs7Ozs7OzhCQUM3Qiw4REFBQ0wsaUVBQU1BOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IE5hdmJhciBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC9OYXZiYXJcIjtcbmltcG9ydCBGb290ZXIgZnJvbSBcIkAvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiV2ViVHJ1eWVuIC0gWW91ciBTdG9yeSBIdWJcIixcbiAgZGVzY3JpcHRpb246IFwiQSBtb2Rlcm4gd2ViIGFwcGxpY2F0aW9uIGZvciBzY3JhcGluZyBhbmQgcmVhZGluZyBzdG9yaWVzLlwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJoLWZ1bGxcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBmbGV4IGZsZXgtY29sIGgtZnVsbCBiZy16aW5jLTkwMCB0ZXh0LXdoaXRlYH0+XG4gICAgICAgIDxOYXZiYXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC1ncm93XCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPEZvb3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJOYXZiYXIiLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400\",\n                    children: \"Đang tải...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFFRCxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby01MDAgbWItNFwiPjwvZGl2PlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+xJBhbmcgdOG6o2kuLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-24 h-24 text-gray-400 mx-auto mb-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1,\n                                d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v6.414c0 .796-.316 1.559-.879 2.121l-7.07 7.071a2 2 0 01-2.829 0L6.151 17.535A2.99 2.99 0 015.272 15.5L12 8.772l6.728 6.728a2.99 2.99 0 01-.879 2.121z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-white mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-300 mb-4\",\n                            children: \"Kh\\xf4ng t\\xecm thấy trang\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-8 max-w-md mx-auto\",\n                            children: \"Trang bạn đang t\\xecm kiếm c\\xf3 thể đ\\xe3 bị x\\xf3a, đổi t\\xean hoặc tạm thời kh\\xf4ng khả dụng.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                \"Về trang chủ\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/scrape\",\n                                className: \"inline-flex items-center px-4 py-2 text-indigo-400 hover:text-indigo-300 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"C\\xe0o truyện mới\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/stories/[storyId]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/stories/[storyId]/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoryPage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_storyService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/storyService */ \"(rsc)/./src/services/storyService.ts\");\n/* harmony import */ var _components_story_StoryInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/story/StoryInfo */ \"(rsc)/./src/components/story/StoryInfo.tsx\");\n/* harmony import */ var _components_story_ChapterList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/story/ChapterList */ \"(rsc)/./src/components/story/ChapterList.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n\n\n\n\nasync function StoryPage({ params }) {\n    try {\n        const [story, chaptersResponse] = await Promise.all([\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchStoryById)(params.storyId),\n            (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchChaptersByStoryId)(params.storyId, 1, 100)\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_story_StoryInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    story: story\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_story_ChapterList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    storyId: params.storyId,\n                    initialChapters: chaptersResponse.data,\n                    totalChapters: chaptersResponse.pagination.total_items\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\page.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\app\\\\stories\\\\[storyId]\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this);\n    } catch (error) {\n        console.error(\"Error fetching story:\", error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.notFound)();\n    }\n}\nasync function generateMetadata({ params }) {\n    try {\n        const story = await (0,_services_storyService__WEBPACK_IMPORTED_MODULE_1__.fetchStoryById)(params.storyId);\n        return {\n            title: `${story.title} - WebTruyen`,\n            description: story.description || `Đọc truyện ${story.title} của tác giả ${story.author}`\n        };\n    } catch  {\n        return {\n            title: \"Story Not Found - WebTruyen\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/stories/[storyId]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-zinc-800/30 border-t border-gray-700 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" WebTruyen. All rights reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsTUFBTUEsU0FBUztJQUNiLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFOzt3QkFBRTt3QkFBUSxJQUFJQyxPQUFPQyxXQUFXO3dCQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzlDO0FBRUEsaUVBQWVOLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4PzI2MzgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRm9vdGVyID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctemluYy04MDAvMzAgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwIG10LWF1dG9cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHktNCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgIDxwPiZjb3B5OyB7bmV3IERhdGUoKS5nZXRGdWxsWWVhcigpfSBXZWJUcnV5ZW4uIEFsbCByaWdodHMgcmVzZXJ2ZWQuPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRm9vdGVyOyJdLCJuYW1lcyI6WyJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwiRGF0ZSIsImdldEZ1bGxZZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nconst Navbar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-zinc-800/30 border-b border-gray-700 backdrop-blur-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"text-white font-bold text-xl\",\n                            children: \"WebTruyen\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-10 flex items-baseline space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/scrape\",\n                                    className: \"text-gray-300 hover:bg-zinc-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Scrape\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/story/ChapterList.tsx":
/*!**********************************************!*\
  !*** ./src/components/story/ChapterList.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Personal Projects\Vibe\Webtruyen\FE\src\components\story\ChapterList.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/story/StoryInfo.tsx":
/*!********************************************!*\
  !*** ./src/components/story/StoryInfo.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n\n\nconst StoryInfo = ({ story })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-zinc-800/50 rounded-lg p-6 mb-8 border border-zinc-700/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col md:flex-row gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-48 h-64 mx-auto md:mx-0 bg-zinc-700 rounded-lg overflow-hidden\",\n                        children: story.cover_image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: story.cover_image_url,\n                            alt: story.title,\n                            fill: true,\n                            className: \"object-cover\",\n                            sizes: \"192px\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-16 h-16\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-2\",\n                                    children: story.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300\",\n                                    children: [\n                                        \"T\\xe1c giả: \",\n                                        story.author\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700/50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Trạng th\\xe1i\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex px-3 py-1 text-sm font-medium rounded-full ${story.status === \"completed\" ? \"bg-green-600 text-green-100\" : story.status === \"ongoing\" ? \"bg-blue-600 text-blue-100\" : \"bg-yellow-600 text-yellow-100\"}`,\n                                            children: story.status === \"completed\" ? \"Ho\\xe0n th\\xe0nh\" : story.status === \"ongoing\" ? \"Đang ra\" : \"Tạm dừng\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700/50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Số chương\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: story.total_chapters_scraped\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700/50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Nguồn\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: story.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-indigo-400 hover:text-indigo-300 transition-colors\",\n                                            children: story.url\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700/50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Cập nhật\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: new Date(story.updated_at).toLocaleDateString(\"vi-VN\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        story.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-zinc-700/50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-400 mb-2\",\n                                    children: \"M\\xf4 tả\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 leading-relaxed whitespace-pre-wrap\",\n                                    children: story.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Personal Projects\\\\Vibe\\\\Webtruyen\\\\FE\\\\src\\\\components\\\\story\\\\StoryInfo.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StoryInfo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9zdG9yeS9TdG9yeUluZm8udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQStCO0FBTy9CLE1BQU1DLFlBQVksQ0FBQyxFQUFFQyxLQUFLLEVBQWtCO0lBQzFDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNaRixNQUFNRyxlQUFlLGlCQUNwQiw4REFBQ0wsa0RBQUtBOzRCQUNKTSxLQUFLSixNQUFNRyxlQUFlOzRCQUMxQkUsS0FBS0wsTUFBTU0sS0FBSzs0QkFDaEJDLElBQUk7NEJBQ0pMLFdBQVU7NEJBQ1ZNLE9BQU07NEJBQ05DLFFBQVE7Ozs7O3NEQUdWLDhEQUFDUjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ1E7Z0NBQUlSLFdBQVU7Z0NBQVlLLE1BQUs7Z0NBQWVJLFNBQVE7MENBQ3JELDRFQUFDQztvQ0FBS0MsVUFBUztvQ0FBVUMsR0FBRTtvQ0FBNkZDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVEzSSw4REFBQ2Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNjO29DQUFHZCxXQUFVOzhDQUFzQ0YsTUFBTU0sS0FBSzs7Ozs7OzhDQUMvRCw4REFBQ1c7b0NBQUVmLFdBQVU7O3dDQUF3Qjt3Q0FBVUYsTUFBTWtCLE1BQU07Ozs7Ozs7Ozs7Ozs7c0NBRzdELDhEQUFDakI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNpQjs0Q0FBR2pCLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDa0I7NENBQUtsQixXQUFXLENBQUMsdURBQXVELEVBQ3ZFRixNQUFNcUIsTUFBTSxLQUFLLGNBQWMsZ0NBQy9CckIsTUFBTXFCLE1BQU0sS0FBSyxZQUFZLDhCQUM3QixnQ0FDRCxDQUFDO3NEQUNDckIsTUFBTXFCLE1BQU0sS0FBSyxjQUFjLHFCQUMvQnJCLE1BQU1xQixNQUFNLEtBQUssWUFBWSxZQUFZOzs7Ozs7Ozs7Ozs7OENBSTlDLDhEQUFDcEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDaUI7NENBQUdqQixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ2U7NENBQUVmLFdBQVU7c0RBQW9DRixNQUFNc0Isc0JBQXNCOzs7Ozs7Ozs7Ozs7OENBRy9FLDhEQUFDckI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDaUI7NENBQUdqQixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ3FCOzRDQUNDQyxNQUFNeEIsTUFBTXlCLEdBQUc7NENBQ2ZDLFFBQU87NENBQ1BDLEtBQUk7NENBQ0p6QixXQUFVO3NEQUVURixNQUFNeUIsR0FBRzs7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDeEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDaUI7NENBQUdqQixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ2U7NENBQUVmLFdBQVU7c0RBQWMsSUFBSTBCLEtBQUs1QixNQUFNNkIsVUFBVSxFQUFFQyxrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQUk1RTlCLE1BQU0rQixXQUFXLGtCQUNoQiw4REFBQzlCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2lCO29DQUFHakIsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNlO29DQUFFZixXQUFVOzhDQUNWRixNQUFNK0IsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRbEM7QUFFQSxpRUFBZWhDLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZS8uL3NyYy9jb21wb25lbnRzL3N0b3J5L1N0b3J5SW5mby50c3g/NDM0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgeyBTdG9yeSB9IGZyb20gJ0AvdHlwZXMvc3RvcnknO1xuXG5pbnRlcmZhY2UgU3RvcnlJbmZvUHJvcHMge1xuICBzdG9yeTogU3Rvcnk7XG59XG5cbmNvbnN0IFN0b3J5SW5mbyA9ICh7IHN0b3J5IH06IFN0b3J5SW5mb1Byb3BzKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy16aW5jLTgwMC81MCByb3VuZGVkLWxnIHAtNiBtYi04IGJvcmRlciBib3JkZXItemluYy03MDAvNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBnYXAtNlwiPlxuICAgICAgICB7LyogQ292ZXIgSW1hZ2UgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy00OCBoLTY0IG14LWF1dG8gbWQ6bXgtMCBiZy16aW5jLTcwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAge3N0b3J5LmNvdmVyX2ltYWdlX3VybCA/IChcbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgc3JjPXtzdG9yeS5jb3Zlcl9pbWFnZV91cmx9XG4gICAgICAgICAgICAgICAgYWx0PXtzdG9yeS50aXRsZX1cbiAgICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICBzaXplcz1cIjE5MnB4XCJcbiAgICAgICAgICAgICAgICBwcmlvcml0eVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGwgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0xNiBoLTE2XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCAzYTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY1YTIgMiAwIDAwLTItMkg0em0xMiAxMkg0bDQtOCAzIDYgMi00IDMgNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdG9yeSBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+e3N0b3J5LnRpdGxlfTwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS0zMDBcIj5Uw6FjIGdp4bqjOiB7c3RvcnkuYXV0aG9yfTwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXppbmMtNzAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPlRy4bqhbmcgdGjDoWk8L2gzPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBweC0zIHB5LTEgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICBzdG9yeS5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ2JnLWdyZWVuLTYwMCB0ZXh0LWdyZWVuLTEwMCcgOlxuICAgICAgICAgICAgICAgIHN0b3J5LnN0YXR1cyA9PT0gJ29uZ29pbmcnID8gJ2JnLWJsdWUtNjAwIHRleHQtYmx1ZS0xMDAnIDpcbiAgICAgICAgICAgICAgICAnYmcteWVsbG93LTYwMCB0ZXh0LXllbGxvdy0xMDAnXG4gICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICB7c3Rvcnkuc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdIb8OgbiB0aMOgbmgnIDpcbiAgICAgICAgICAgICAgICAgc3Rvcnkuc3RhdHVzID09PSAnb25nb2luZycgPyAnxJBhbmcgcmEnIDogJ1ThuqFtIGThu6tuZyd9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXppbmMtNzAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPlPhu5EgY2jGsMahbmc8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPntzdG9yeS50b3RhbF9jaGFwdGVyc19zY3JhcGVkfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXppbmMtNzAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPk5ndeG7k248L2gzPlxuICAgICAgICAgICAgICA8YSBcbiAgICAgICAgICAgICAgICBocmVmPXtzdG9yeS51cmx9IFxuICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiIFxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTQwMCBob3Zlcjp0ZXh0LWluZGlnby0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3N0b3J5LnVybH1cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctemluYy03MDAvNTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBtYi0xXCI+Q+G6rXAgbmjhuq10PC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntuZXcgRGF0ZShzdG9yeS51cGRhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoJ3ZpLVZOJyl9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7c3RvcnkuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy16aW5jLTcwMC81MCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTJcIj5Nw7QgdOG6ozwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgbGVhZGluZy1yZWxheGVkIHdoaXRlc3BhY2UtcHJlLXdyYXBcIj5cbiAgICAgICAgICAgICAgICB7c3RvcnkuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTdG9yeUluZm87Il0sIm5hbWVzIjpbIkltYWdlIiwiU3RvcnlJbmZvIiwic3RvcnkiLCJkaXYiLCJjbGFzc05hbWUiLCJjb3Zlcl9pbWFnZV91cmwiLCJzcmMiLCJhbHQiLCJ0aXRsZSIsImZpbGwiLCJzaXplcyIsInByaW9yaXR5Iiwic3ZnIiwidmlld0JveCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsImgxIiwicCIsImF1dGhvciIsImgzIiwic3BhbiIsInN0YXR1cyIsInRvdGFsX2NoYXB0ZXJzX3NjcmFwZWQiLCJhIiwiaHJlZiIsInVybCIsInRhcmdldCIsInJlbCIsIkRhdGUiLCJ1cGRhdGVkX2F0IiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGVzY3JpcHRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/story/StoryInfo.tsx\n");

/***/ }),

/***/ "(rsc)/./src/services/storyService.ts":
/*!**************************************!*\
  !*** ./src/services/storyService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchChapterById: () => (/* binding */ fetchChapterById),\n/* harmony export */   fetchChapterByNumber: () => (/* binding */ fetchChapterByNumber),\n/* harmony export */   fetchChaptersByStoryId: () => (/* binding */ fetchChaptersByStoryId),\n/* harmony export */   fetchStories: () => (/* binding */ fetchStories),\n/* harmony export */   fetchStoryById: () => (/* binding */ fetchStoryById)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\";\n// Fetch all stories with pagination\nconst fetchStories = async (page = 1, pageSize = 20)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch stories\");\n    }\n    return response.json();\n};\n// Fetch a single story by ID\nconst fetchStoryById = async (storyId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch story\");\n    }\n    return response.json();\n};\n// Fetch chapters for a story\nconst fetchChaptersByStoryId = async (storyId, page = 1, pageSize = 50)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters?page=${page}&page_size=${pageSize}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapters\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by chapter number\nconst fetchChapterByNumber = async (storyId, chapterNumber)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/stories/${storyId}/chapters/${chapterNumber}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n// Fetch a single chapter by ID\nconst fetchChapterById = async (chapterId)=>{\n    const response = await fetch(`${API_BASE_URL}/api/v1/stories/chapters/${chapterId}`);\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch chapter\");\n    }\n    return response.json();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/storyService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstories%2F%5BstoryId%5D%2Fpage&page=%2Fstories%2F%5BstoryId%5D%2Fpage&appPaths=%2Fstories%2F%5BstoryId%5D%2Fpage&pagePath=private-next-app-dir%2Fstories%2F%5BstoryId%5D%2Fpage.tsx&appDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPersonal%20Projects%5CVibe%5CWebtruyen%5CFE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();