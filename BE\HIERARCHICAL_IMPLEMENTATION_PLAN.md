# Hierarchical Scraping Implementation Plan

## Overview

This document outlines the implementation of a new hierarchical scraping workflow that follows the **Story → Page → Chapter** structure. The implementation maintains existing functionality while adding the new workflow as an alternative approach.

## Architecture Changes

### 1. Database Model Updates

#### New Page Model
- **File**: `API/models/page.py` (NEW)
- **Purpose**: Represents individual pages containing chapter lists
- **Fields**:
  - `story_id`: Reference to parent story
  - `page_number`: Sequential page number
  - `page_url`: URL of the page
  - `chapter_urls`: List of chapter URLs found on this page
  - `total_chapters_on_page`: Count of chapters on this page
  - `is_scraped`: Scraping status
  - `metadata`: Page-specific metadata

#### Updated Database Models
- **File**: `API/models/database.py` (UPDATED)
- **Changes**:
  - Added `Page` and `PageMetadata` models
  - Updated `Chapter` model to include `page_id` field
  - Updated `Story` model to include `total_pages` and `total_pages_scraped` fields
  - Updated `StoryMetadata` to include `total_pages` field
  - Added database indexes for the new Page collection

### 2. Configuration Updates

#### CSS Selector Enhancement
- **File**: `Scraper/config.yaml` (UPDATED)
- **Changes**:
  - Added `story_total_chapters` selector: `'span:contains("Chương"), .chapter-count'`
  - This selector targets elements containing "239 Chương" as shown in the provided image

### 3. Scraping Strategy Updates

#### Enhanced WebtruyenStrategy
- **File**: `Scraper/src/scraping_strategy.py` (UPDATED)
- **Changes**:
  - Added `_extract_total_chapters_count()` method
  - Enhanced `_extract_additional_metadata()` to include total chapters
  - Improved regex pattern to extract numbers from "239 Chương" format
  - Added fallback extraction for various number formats

### 4. New Hierarchical Service

#### HierarchicalScrapingService
- **File**: `API/services/hierarchical_scraping_service.py` (NEW)
- **Purpose**: Implements the three-phase scraping workflow
- **Phases**:
  1. **Story Info Scraping**: Extract metadata and generate page URLs
  2. **Page Processing**: Extract chapter URLs from each page
  3. **Database Storage**: Store complete hierarchy

#### Key Methods:
- `scrape_story_hierarchy()`: Main orchestration method
- `_scrape_story_info()`: Phase 1 implementation
- `_process_pages()`: Phase 2 implementation
- `_store_hierarchy()`: Phase 3 implementation
- `scrape_chapter_content()`: Individual chapter content scraping

### 5. New API Endpoints

#### Hierarchical Scraping Router
- **File**: `API/routers/hierarchical_scraping.py` (NEW)
- **Endpoints**:
  - `POST /api/v1/hierarchical/scrape` - Complete hierarchical scraping
  - `GET /api/v1/hierarchical/story/{story_id}` - Get story hierarchy
  - `GET /api/v1/hierarchical/story/{story_id}/pages` - Get story pages
  - `GET /api/v1/hierarchical/page/{page_id}/chapters` - Get page chapters
  - `POST /api/v1/hierarchical/chapter/{chapter_id}/content` - Scrape chapter content
  - `GET /api/v1/hierarchical/stats` - Get system statistics

#### Updated Application
- **File**: `API/core/app.py` (UPDATED)
- **Changes**:
  - Added import for `hierarchical_scraping` router
  - Included new router in application with `/api/v1/hierarchical` prefix

## Implementation Workflow

### Phase 1: Story Info Scraping
1. Extract story metadata (title, author, description, cover image)
2. Extract total chapters count using new CSS selector
3. Determine total pages using existing pagination logic
4. Generate all page URLs for the story

### Phase 2: Page Processing
1. Iterate through each page URL
2. Extract chapter URLs from each page
3. Store page information with chapter URLs
4. Track chapters per page for statistics

### Phase 3: Database Storage
1. Create/update Story document
2. Create Page documents for each page
3. Create Chapter documents linked to pages
4. Update statistics and relationships

### Phase 4: Content Scraping (Optional)
1. Scrape individual chapter content on demand
2. Update chapter documents with content
3. Track scraping progress

## API Usage Examples

### Complete Hierarchical Scraping
```bash
curl -X POST "http://localhost:8000/api/v1/hierarchical/scrape" \
  -H "Content-Type: application/json" \
  -d '{
    "story_url": "https://webtruyen.diendantruyen.com/story-url",
    "max_pages": 10,
    "scrape_content": false
  }'
```

### Get Story Hierarchy
```bash
curl "http://localhost:8000/api/v1/hierarchical/story/{story_id}?include_chapters=true"
```

### Get Page Chapters
```bash
curl "http://localhost:8000/api/v1/hierarchical/page/{page_id}/chapters"
```

### Scrape Chapter Content
```bash
curl -X POST "http://localhost:8000/api/v1/hierarchical/chapter/{chapter_id}/content"
```

## Database Schema

### Collections Structure
```
stories
├── _id (ObjectId)
├── title (String)
├── url (String)
├── total_pages (Integer)
├── total_pages_scraped (Integer)
├── total_chapters_scraped (Integer)
├── metadata (Object)
│   ├── author (String)
│   ├── description (String)
│   ├── total_chapters (Integer)
│   ├── total_pages (Integer)
│   └── cover_image_url (String)
└── timestamps

pages
├── _id (ObjectId)
├── story_id (ObjectId) → stories._id
├── page_number (Integer)
├── page_url (String)
├── chapter_urls (Array[String])
├── total_chapters_on_page (Integer)
├── is_scraped (Boolean)
└── timestamps

chapters
├── _id (ObjectId)
├── story_id (ObjectId) → stories._id
├── page_id (ObjectId) → pages._id
├── chapter_number (Integer)
├── title (String)
├── url (String)
├── original_content (String)
├── is_scraped (Boolean)
└── timestamps
```

## Benefits of New Architecture

1. **Clear Hierarchy**: Explicit Story → Page → Chapter relationships
2. **Scalable Processing**: Pages can be processed independently
3. **Better Progress Tracking**: Track progress at story, page, and chapter levels
4. **Flexible Content Scraping**: Scrape content on-demand or in batches
5. **Improved Error Handling**: Isolate failures to specific pages/chapters
6. **Enhanced Statistics**: Detailed metrics at each level
7. **Backward Compatibility**: Existing APIs continue to work

## Migration Strategy

### For Existing Data
1. **No immediate migration required** - new workflow creates new documents
2. **Gradual transition** - use new workflow for new stories
3. **Optional migration script** - can be created to migrate existing data

### For Existing APIs
1. **Maintain existing endpoints** - no breaking changes
2. **Add new hierarchical endpoints** - parallel implementation
3. **Deprecation path** - gradually migrate clients to new endpoints

## Testing Strategy

### Unit Tests
- Test individual service methods
- Test database model validation
- Test CSS selector extraction

### Integration Tests
- Test complete workflow end-to-end
- Test API endpoints
- Test database operations

### Performance Tests
- Test with large stories (many pages/chapters)
- Test concurrent scraping
- Test database query performance

## Deployment Steps

1. **Database Updates**:
   ```bash
   # Database indexes will be created automatically on startup
   # No manual migration required
   ```

2. **Application Restart**:
   ```bash
   # Restart the API server to load new code
   # New endpoints will be available immediately
   ```

3. **Verification**:
   ```bash
   # Test new endpoints
   curl "http://localhost:8000/api/v1/hierarchical/stats"
   
   # Test complete workflow
   curl -X POST "http://localhost:8000/api/v1/hierarchical/scrape" \
     -H "Content-Type: application/json" \
     -d '{"story_url": "test-url", "max_pages": 2}'
   ```

## Monitoring and Maintenance

### Key Metrics
- Stories processed per day
- Pages scraped per story
- Chapters extracted per page
- Content scraping success rate
- Database growth rate

### Error Monitoring
- Failed page extractions
- Missing chapter URLs
- Database connection issues
- Scraping timeouts

### Performance Optimization
- Database query optimization
- Concurrent processing limits
- Memory usage monitoring
- Rate limiting adjustments

## Future Enhancements

1. **Batch Processing**: Process multiple stories in parallel
2. **Smart Retry**: Retry failed pages/chapters with exponential backoff
3. **Content Validation**: Validate extracted content quality
4. **Caching**: Cache page content to reduce re-scraping
5. **Analytics**: Advanced analytics on scraping patterns
6. **Auto-Discovery**: Automatically discover new stories

## Conclusion

This implementation provides a robust, scalable foundation for hierarchical web scraping while maintaining backward compatibility. The new architecture supports the required Story → Page → Chapter workflow and provides comprehensive APIs for managing the entire process.

The implementation is ready for production use and can be gradually adopted alongside existing functionality.