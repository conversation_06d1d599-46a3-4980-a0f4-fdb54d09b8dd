import { ScrapeRequest, ScrapeResponse } from '@/types/scrape';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const scrapeStory = async (url: string): Promise<ScrapeResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/tasks/scraping/story-info`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        story_url: url,
        max_pages: 10
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      message: data.message || 'Story scraped successfully',
      storyId: data.data?.story_id
    };
  } catch (error) {
    console.error('Error scraping story:', error);
    throw error;
  }
};