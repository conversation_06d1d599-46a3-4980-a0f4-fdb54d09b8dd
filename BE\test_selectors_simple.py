import asyncio
from playwright.async_api import async_playwright
import yaml

async def test_selectors():
    # Load config
    with open('BE/Scraper/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    selectors = config['targets']['webtruyen']['selectors']
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        url = "https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/"
        print(f"🚀 Testing selectors on: {url}")
        
        await page.goto(url, wait_until='networkidle')
        
        # Test author selector
        print(f"\n📝 Testing author selector: {selectors['story_author']}")
        try:
            author_element = await page.query_selector(selectors['story_author'])
            if author_element:
                author_text = await author_element.inner_text()
                print(f"✅ Author found: '{author_text.strip()}'")
            else:
                print("❌ Author element not found")
        except Exception as e:
            print(f"❌ Error testing author selector: {e}")
        
        # Test pagination selector
        print(f"\n📄 Testing pagination selector: {selectors['story_pagination']}")
        try:
            pagination_element = await page.query_selector(selectors['story_pagination'])
            if pagination_element:
                pagination_text = await pagination_element.inner_text()
                href = await pagination_element.get_attribute('href')
                print(f"✅ Pagination found: '{pagination_text.strip()}' - href: {href}")
            else:
                print("❌ Pagination element not found")
        except Exception as e:
            print(f"❌ Error testing pagination selector: {e}")
        
        # Test chapter links
        print(f"\n📚 Testing chapter links: {selectors['chapter_link']}")
        try:
            chapter_elements = await page.query_selector_all(selectors['chapter_link'])
            print(f"✅ Found {len(chapter_elements)} chapter links")
            if chapter_elements:
                for i, element in enumerate(chapter_elements[:3]):  # Show first 3
                    text = await element.inner_text()
                    href = await element.get_attribute('href')
                    print(f"  Chapter {i+1}: '{text.strip()[:50]}...' - {href}")
        except Exception as e:
            print(f"❌ Error testing chapter selector: {e}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_selectors())