import asyncio
from playwright.async_api import async_playwright

async def test_selector():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        url = "https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap"
        print(f"Navigating to: {url}")
        
        await page.goto(url, wait_until='networkidle')
        print("Page loaded successfully")
        
        # Test the configured selector
        selector = '#category-title'
        print(f"Testing selector: {selector}")
        
        title_element = await page.query_selector(selector)
        if title_element:
            title_text = await title_element.inner_text()
            print(f"Found title: '{title_text}'")
        else:
            print(f"No element found for selector: {selector}")
            
        # Try alternative selectors
        alternative_selectors = [
            'h1',
            '.title',
            '.story-title',
            '[class*="title"]',
            'h1, h2, h3'
        ]
        
        for alt_selector in alternative_selectors:
            print(f"\nTrying alternative selector: {alt_selector}")
            element = await page.query_selector(alt_selector)
            if element:
                text = await element.inner_text()
                print(f"Found: '{text}'")
            else:
                print("Not found")

        # Test description selector
        desc_selector = 'div.hide-long-text.uk-position-relative.uk-text-justify'
        print(f"\nTesting description selector: {desc_selector}")
        desc_element = await page.query_selector(desc_selector)
        if desc_element:
            desc_text = await desc_element.inner_text()
            print(f"Found description: '{desc_text[:100]}...'")  # Print first 100 chars
        else:
            print("No description element found")

        # Test cover image selector
        img_selector = 'img[src^="https://i0.wp"]'
        print(f"\nTesting cover image selector: {img_selector}")
        img_element = await page.query_selector(img_selector)
        if img_element:
            img_src = await img_element.get_attribute('src')
            print(f"Found cover image URL: {img_src}")
        else:
            print("No cover image found")

        # Test navigation selector
        nav_selector = 'ul.uk-pagination.uk-flex-center'
        print(f"\nTesting navigation selector: {nav_selector}")
        nav_element = await page.query_selector(nav_selector)
        if nav_element:
            nav_items = await nav_element.query_selector_all('li')
            print(f"Found navigation with {len(nav_items)} items")
            for item in nav_items:
                text = await item.inner_text()
                print(f"Navigation item: {text}")
        else:
            print("No navigation element found")
        
        # Get page title
        page_title = await page.title()
        print(f"\nPage title: {page_title}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_selector())