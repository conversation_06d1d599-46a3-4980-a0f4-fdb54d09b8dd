import requests
import json

API_URL = "http://localhost:8000/api/v1/hierarchical/scrape"
STORY_URL = "https://webtruyen.com/truyen-phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/"

headers = {
    "Content-Type": "application/json"
}

data = {
    "story_url": STORY_URL
}

print(f"Sending POST request to {API_URL} with URL: {STORY_URL}")

try:
    response = requests.post(API_URL, headers=headers, data=json.dumps(data))
    response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)

    print("\nResponse status code:", response.status_code)
    print("\nResponse JSON:")
    print(json.dumps(response.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"\nAn error occurred: {e}")
    if e.response:
        print("\nError Response content:")
        print(e.response.text)
