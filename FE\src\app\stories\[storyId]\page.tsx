import { fetchStoryById, fetchChaptersByStoryId } from '@/services/storyService';
import StoryInfo from '@/components/story/StoryInfo';
import ChapterList from '@/components/story/ChapterList';
import { notFound } from 'next/navigation';
import { Story, Chapter, PaginatedAPIResponse } from '@/types/story';

interface StoryPageProps {
  params: {
    storyId: string;
  };
}

export default async function StoryPage({ params }: StoryPageProps) {
  try {
    const [story, chaptersResponse] = await Promise.all([
      fetchStoryById(params.storyId),
      fetchChaptersByStoryId(params.storyId, 1, 100)
    ]);

    return (
      <div className="container mx-auto px-4 py-8">
        <StoryInfo story={story} />
        <ChapterList
          storyId={params.storyId}
          initialChapters={chaptersResponse.data}
          totalChapters={chaptersResponse.pagination.total_items}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching story:', error);
    notFound();
  }
}

export async function generateMetadata({ params }: StoryPageProps) {
  try {
    const story = await fetchStoryById(params.storyId);
    return {
      title: `${story.title} - WebTruyen`,
      description: story.description || `Đọc truyện ${story.title} của tác giả ${story.author}`,
    };
  } catch {
    return {
      title: 'Story Not Found - WebTruyen',
    };
  }
}