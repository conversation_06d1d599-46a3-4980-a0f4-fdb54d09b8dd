'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Chapter } from '@/types/story';
import { fetchChaptersByStoryId } from '@/services/storyService';

interface ChapterListProps {
  storyId: string;
  initialChapters: Chapter[];
  totalChapters: number;
}

const ChapterList = ({ storyId, initialChapters, totalChapters }: ChapterListProps) => {
  const [chapters, setChapters] = useState<Chapter[]>(initialChapters);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialChapters.length < totalChapters);

  const loadMoreChapters = async () => {
    if (loading || !hasMore) return;

    try {
      setLoading(true);
      const nextPage = page + 1;
      const response = await fetchChaptersByStoryId(storyId, nextPage, 50);
      
      setChapters(prev => [...prev, ...response.data]);
        setPage(nextPage);
        setHasMore(response.pagination.has_next);
    } catch (error) {
      console.error('Error loading more chapters:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-zinc-800/50 rounded-lg p-6 border border-zinc-700/50">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white">Danh sách chương</h2>
        <span className="text-gray-400">{totalChapters} chương</span>
      </div>

      {chapters.length === 0 ? (
        <div className="text-center py-8">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
          </svg>
          <p className="text-gray-400">Chưa có chương nào được tải</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {chapters.map((chapter) => (
              <Link
                 key={chapter.id}
                href={`/stories/${storyId}/${chapter.chapter_number}`}
                className="group block bg-zinc-700/50 hover:bg-zinc-700 rounded-lg p-4 transition-all duration-200 border border-zinc-600/50 hover:border-indigo-500/50"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white group-hover:text-indigo-400 transition-colors truncate">
                      Chương {chapter.chapter_number}
                    </h3>
                    {chapter.title && (
                      <p className="text-sm text-gray-400 mt-1 truncate">
                        {chapter.title}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                      <span>{(chapter.word_count || 0).toLocaleString()} từ</span>
                      <span>{new Date(chapter.updated_at).toLocaleDateString('vi-VN')}</span>
                    </div>
                    <div className="flex gap-2 mt-1">
                      {chapter.is_scraped && (
                        <span className="text-xs text-green-400 bg-green-900/30 px-2 py-1 rounded">
                          Đã cào
                        </span>
                      )}
                      {chapter.is_enhanced && (
                        <span className="text-xs text-blue-400 bg-blue-900/30 px-2 py-1 rounded">
                          Đã nâng cấp
                        </span>
                      )}
                    </div>
                  </div>
                  <svg 
                    className="w-5 h-5 text-gray-400 group-hover:text-indigo-400 transition-colors ml-2" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </Link>
            ))}
          </div>

          {hasMore && (
            <div className="text-center mt-6">
              <button
                onClick={loadMoreChapters}
                disabled={loading}
                className="bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-md transition-colors"
              >
                {loading ? 'Đang tải...' : 'Tải thêm chương'}
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ChapterList;