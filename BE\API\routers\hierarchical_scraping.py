"""Hierarchical Scraping API Routes

Provides endpoints for the new Story → Page → Chapter workflow:
1. POST /hierarchical/scrape - Complete hierarchical scraping
2. GET /hierarchical/story/{story_id} - Get story hierarchy
3. GET /hierarchical/story/{story_id}/pages - Get story pages
4. GET /hierarchical/page/{page_id}/chapters - Get page chapters
5. POST /hierarchical/chapter/{chapter_id}/content - Scrape chapter content
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from pydantic import BaseModel, HttpUrl

from API.services.hierarchical_scraping_service import get_hierarchical_scraping_service
from API.models.database import db_manager
from API.middleware.error_handling import ScrapingError
from API.utils.logging_config import LoggerMixin


router = APIRouter(tags=["Hierarchical Scraping"])


class HierarchicalScrapeRequest(BaseModel):
    """Request model for hierarchical scraping"""
    story_url: HttpUrl
    max_pages: int = 10
    scrape_content: bool = False  # Whether to also scrape chapter content


class HierarchicalScrapeResponse(BaseModel):
    """Response model for hierarchical scraping"""
    success: bool
    story_id: str
    title: str
    total_pages: int
    total_chapters: int
    pages_scraped: int
    chapters_created: int
    message: str
    data: Optional[Dict[str, Any]] = None


class StoryHierarchyResponse(BaseModel):
    """Response model for story hierarchy"""
    story_id: str
    title: str
    url: str
    total_pages: int
    total_chapters_scraped: int
    metadata: Dict[str, Any]
    pages: List[Dict[str, Any]]
    created_at: str
    updated_at: str


class PageChaptersResponse(BaseModel):
    """Response model for page chapters"""
    page_id: str
    page_number: int
    page_url: str
    total_chapters_on_page: int
    chapters: List[Dict[str, Any]]


class ChapterContentResponse(BaseModel):
    """Response model for chapter content"""
    chapter_id: str
    title: str
    content: str
    word_count: Optional[int] = None
    scraped_at: str


@router.post("/scrape", response_model=HierarchicalScrapeResponse)
async def scrape_story_hierarchical(
    request_data: HierarchicalScrapeRequest,
    request: Request,
    service = Depends(get_hierarchical_scraping_service)
) -> HierarchicalScrapeResponse:
    """Perform complete hierarchical scraping of a story
    
    This endpoint implements the full Story → Page → Chapter workflow:
    1. Extracts story metadata and generates page URLs
    2. Processes each page to extract chapter URLs
    3. Stores the complete hierarchy in the database
    4. Optionally scrapes chapter content if requested
    """
    try:
        # Phase 1-3: Complete hierarchy scraping
        result = await service.scrape_story_hierarchy(
            str(request_data.story_url),
            request,
            request_data.max_pages
        )
        
        # Phase 4: Optional content scraping
        chapters_with_content = 0
        if request_data.scrape_content:
            # Get all chapter IDs for this story
            story_id = result['story_id']
            chapters = await db_manager.db.chapters.find({"story_id": story_id}).to_list(None)
            
            # Scrape content for each chapter (with rate limiting)
            for chapter in chapters[:10]:  # Limit to first 10 chapters for demo
                try:
                    await service.scrape_chapter_content(str(chapter['_id']), request)
                    chapters_with_content += 1
                except Exception as e:
                    service.log_warning(f"Failed to scrape content for chapter {chapter['_id']}: {str(e)}")
                    continue
        
        return HierarchicalScrapeResponse(
            success=True,
            story_id=result['story_id'],
            title=result['story']['title'],
            total_pages=result['total_pages'],
            total_chapters=result['total_chapters'],
            pages_scraped=len(result['pages']),
            chapters_created=result['total_chapters'],
            message=f"Successfully scraped story hierarchy with {result['total_chapters']} chapters across {result['total_pages']} pages",
            data=result
        )
        
    except ScrapingError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/story/{story_id}", response_model=StoryHierarchyResponse)
async def get_story_hierarchy(
    story_id: str,
    include_chapters: bool = Query(True, description="Include chapter details in response")
) -> StoryHierarchyResponse:
    """Get complete story hierarchy including pages and chapters"""
    try:
        # Get story document
        story = await db_manager.db.stories.find_one({"_id": story_id})
        if not story:
            raise HTTPException(status_code=404, detail="Story not found")
        
        # Get pages for this story
        pages = await db_manager.db.pages.find({"story_id": story_id}).sort("page_number", 1).to_list(None)
        
        # Optionally include chapters
        pages_with_chapters = []
        for page in pages:
            page_data = {
                "page_id": str(page['_id']),
                "page_number": page['page_number'],
                "page_url": page['page_url'],
                "total_chapters_on_page": page['total_chapters_on_page'],
                "is_scraped": page['is_scraped'],
                "created_at": page['created_at'].isoformat()
            }
            
            if include_chapters:
                chapters = await db_manager.db.chapters.find(
                    {"page_id": page['_id']}
                ).sort("chapter_number", 1).to_list(None)
                
                page_data["chapters"] = [
                    {
                        "chapter_id": str(chapter['_id']),
                        "chapter_number": chapter['chapter_number'],
                        "title": chapter['title'],
                        "url": chapter['url'],
                        "is_scraped": chapter['is_scraped'],
                        "is_enhanced": chapter['is_enhanced']
                    }
                    for chapter in chapters
                ]
            
            pages_with_chapters.append(page_data)
        
        return StoryHierarchyResponse(
            story_id=str(story['_id']),
            title=story['title'],
            url=story['url'],
            total_pages=story.get('total_pages', 0),
            total_chapters_scraped=story.get('total_chapters_scraped', 0),
            metadata=story.get('metadata', {}),
            pages=pages_with_chapters,
            created_at=story['created_at'].isoformat(),
            updated_at=story['updated_at'].isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/story/{story_id}/pages")
async def get_story_pages(
    story_id: str,
    page_number: Optional[int] = Query(None, description="Specific page number to retrieve")
) -> Dict[str, Any]:
    """Get pages for a specific story"""
    try:
        # Build query
        query = {"story_id": story_id}
        if page_number is not None:
            query["page_number"] = page_number
        
        # Get pages
        pages = await db_manager.db.pages.find(query).sort("page_number", 1).to_list(None)
        
        if not pages:
            raise HTTPException(status_code=404, detail="No pages found")
        
        return {
            "story_id": story_id,
            "total_pages": len(pages),
            "pages": [
                {
                    "page_id": str(page['_id']),
                    "page_number": page['page_number'],
                    "page_url": page['page_url'],
                    "total_chapters_on_page": page['total_chapters_on_page'],
                    "chapter_urls": page.get('chapter_urls', []),
                    "is_scraped": page['is_scraped'],
                    "created_at": page['created_at'].isoformat()
                }
                for page in pages
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/page/{page_id}/chapters", response_model=PageChaptersResponse)
async def get_page_chapters(page_id: str) -> PageChaptersResponse:
    """Get all chapters for a specific page"""
    try:
        # Get page document
        page = await db_manager.db.pages.find_one({"_id": page_id})
        if not page:
            raise HTTPException(status_code=404, detail="Page not found")
        
        # Get chapters for this page
        chapters = await db_manager.db.chapters.find(
            {"page_id": page_id}
        ).sort("chapter_number", 1).to_list(None)
        
        return PageChaptersResponse(
            page_id=str(page['_id']),
            page_number=page['page_number'],
            page_url=page['page_url'],
            total_chapters_on_page=page['total_chapters_on_page'],
            chapters=[
                {
                    "chapter_id": str(chapter['_id']),
                    "chapter_number": chapter['chapter_number'],
                    "title": chapter['title'],
                    "url": chapter['url'],
                    "is_scraped": chapter['is_scraped'],
                    "is_enhanced": chapter['is_enhanced'],
                    "word_count": chapter.get('metadata', {}).get('word_count'),
                    "created_at": chapter['created_at'].isoformat()
                }
                for chapter in chapters
            ]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/chapter/{chapter_id}/content", response_model=ChapterContentResponse)
async def scrape_chapter_content(
    chapter_id: str,
    request: Request,
    service = Depends(get_hierarchical_scraping_service)
) -> ChapterContentResponse:
    """Scrape content for a specific chapter"""
    try:
        result = await service.scrape_chapter_content(chapter_id, request)
        
        return ChapterContentResponse(
            chapter_id=result['chapter_id'],
            title=result['title'],
            content=result['content'],
            word_count=len(result['content'].split()) if result['content'] else 0,
            scraped_at=result['scraped_at']
        )
        
    except ScrapingError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/stats")
async def get_hierarchical_stats() -> Dict[str, Any]:
    """Get statistics about the hierarchical scraping system"""
    try:
        # Count documents
        total_stories = await db_manager.db.stories.count_documents({})
        total_pages = await db_manager.db.pages.count_documents({})
        total_chapters = await db_manager.db.chapters.count_documents({})
        
        # Count scraped content
        scraped_pages = await db_manager.db.pages.count_documents({"is_scraped": True})
        scraped_chapters = await db_manager.db.chapters.count_documents({"is_scraped": True})
        enhanced_chapters = await db_manager.db.chapters.count_documents({"is_enhanced": True})
        
        # Get recent activity
        recent_stories = await db_manager.db.stories.find(
            {}, {"title": 1, "created_at": 1, "total_pages": 1}
        ).sort("created_at", -1).limit(5).to_list(None)
        
        return {
            "totals": {
                "stories": total_stories,
                "pages": total_pages,
                "chapters": total_chapters
            },
            "progress": {
                "pages_scraped": scraped_pages,
                "chapters_scraped": scraped_chapters,
                "chapters_enhanced": enhanced_chapters,
                "page_scraping_rate": round(scraped_pages / total_pages * 100, 2) if total_pages > 0 else 0,
                "chapter_scraping_rate": round(scraped_chapters / total_chapters * 100, 2) if total_chapters > 0 else 0,
                "enhancement_rate": round(enhanced_chapters / total_chapters * 100, 2) if total_chapters > 0 else 0
            },
            "recent_stories": [
                {
                    "story_id": str(story['_id']),
                    "title": story['title'],
                    "total_pages": story.get('total_pages', 0),
                    "created_at": story['created_at'].isoformat()
                }
                for story in recent_stories
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")