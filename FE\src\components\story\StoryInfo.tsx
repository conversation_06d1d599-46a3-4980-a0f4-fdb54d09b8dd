import Image from 'next/image';
import { Story } from '@/types/story';

interface StoryInfoProps {
  story: Story;
}

const StoryInfo = ({ story }: StoryInfoProps) => {
  return (
    <div className="bg-zinc-800/50 rounded-lg p-6 mb-8 border border-zinc-700/50">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Cover Image */}
        <div className="flex-shrink-0">
          <div className="relative w-48 h-64 mx-auto md:mx-0 bg-zinc-700 rounded-lg overflow-hidden">
            {story.cover_image_url ? (
              <Image
                src={story.cover_image_url}
                alt={story.title}
                fill
                className="object-cover"
                sizes="192px"
                priority
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>
        </div>

        {/* Story Information */}
        <div className="flex-1">
          <div className="mb-4">
            <h1 className="text-3xl font-bold text-white mb-2">{story.title}</h1>
            <p className="text-lg text-gray-300">Tác giả: {story.author}</p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <div className="bg-zinc-700/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-400 mb-1">Trạng thái</h3>
              <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${
                story.status === 'completed' ? 'bg-green-600 text-green-100' :
                story.status === 'ongoing' ? 'bg-blue-600 text-blue-100' :
                'bg-yellow-600 text-yellow-100'
              }`}>
                {story.status === 'completed' ? 'Hoàn thành' :
                 story.status === 'ongoing' ? 'Đang ra' : 'Tạm dừng'}
              </span>
            </div>

            <div className="bg-zinc-700/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-400 mb-1">Số chương</h3>
              <p className="text-lg font-semibold text-white">{story.total_chapters_scraped}</p>
            </div>

            <div className="bg-zinc-700/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-400 mb-1">Nguồn</h3>
              <a 
                href={story.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-indigo-400 hover:text-indigo-300 transition-colors"
              >
                {story.url}
              </a>
            </div>

            <div className="bg-zinc-700/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-400 mb-1">Cập nhật</h3>
              <p className="text-white">{new Date(story.updated_at).toLocaleDateString('vi-VN')}</p>
            </div>
          </div>

          {story.description && (
            <div className="bg-zinc-700/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Mô tả</h3>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                {story.description}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StoryInfo;