$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    story_url = "https://webtruyen.diendantruyen.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap"
    max_pages = 1
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/tasks/scraping/story-info" -Method POST -Headers $headers -Body $body
    Write-Host "Status Code: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Response: $($_.Exception.Response | ConvertTo-Json)"
}