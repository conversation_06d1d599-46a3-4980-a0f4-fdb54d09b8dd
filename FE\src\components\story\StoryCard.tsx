import Link from 'next/link';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { Story } from '@/types/story';

interface StoryCardProps {
  story: Story;
}

const StoryCard = ({ story }: StoryCardProps) => {
  return (
    <Link href={`/stories/${story.id}`} className="group">
      <div className="bg-zinc-800/50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-zinc-700/50">
        <div className="relative aspect-[3/4] bg-zinc-700">
          <OptimizedImage
            src={story.cover_image_url}
            alt={story.title}
            fill
            className="group-hover:scale-110 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
            quality={80}
          />
          <div className="absolute top-2 right-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              story.status === 'completed' ? 'bg-green-600 text-green-100' :
              story.status === 'ongoing' ? 'bg-blue-600 text-blue-100' :
              'bg-yellow-600 text-yellow-100'
            }`}>
              {story.status === 'completed' ? 'Hoàn thành' :
               story.status === 'ongoing' ? 'Đang ra' : 'Tạm dừng'}
            </span>
          </div>
        </div>
        <div className="p-4">
          <h3 className="font-semibold text-white text-lg mb-2 group-hover:text-indigo-400 transition-colors overflow-hidden">
            <span className="block truncate">{story.title}</span>
          </h3>
          <p className="text-gray-400 text-sm mb-2">
            Tác giả: {story.author}
          </p>
          <p className="text-gray-500 text-sm mb-3 overflow-hidden" style={{display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical'}}>
            {story.description || 'Không có mô tả'}
          </p>
          <div className="flex justify-between items-center text-xs text-gray-500">
            <span>{story.total_chapters_scraped} chương</span>
            <span>{new Date(story.updated_at).toLocaleDateString('vi-VN')}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default StoryCard;